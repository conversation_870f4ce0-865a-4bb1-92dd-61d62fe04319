// TODO - To rework to improve embrace ReScript language philosophy
// (and not js language philosophy)
// TODO - No need to present a React.Context, we should use
// React props drilling here.

include Notifier__Context
module Types = Notifier__Types

type t = {
  get: list<Types.notification>,
  add: (Types.notificationAction, ~details: array<string>=?, unit) => unit,
  reset: (Types.notificationAction, ~details: array<string>=?, unit) => unit,
  clear: unit => unit,
}

let autoClearRef = ref(false)

let createContext = () => {
  let (state, dispatch) = React.useReducer(
    Notifier__Reducer.reducer,
    Notifier__Reducer.initialState,
  )
  let url = Navigation.useUrl()

  React.useEffect1(() => {
    if autoClearRef.contents {
      ClearRequested->dispatch
    }
    None
  }, [url.pathname])

  (Some(state), dispatch)
}

let add = (
  dispatch: Notifier__Reducer.action => unit,
  ~action: Types.notificationAction,
  ~reset=false,
  ~details=?,
  (),
) => {
  let node: Notifier__Reducer.node = switch action {
  | Success(message) => {message, details, kind: #success, reset}
  | Error(message) => {message, details, kind: #error, reset}
  | Warning(message) => {message, details, kind: #warning, reset}
  }

  AddRequested(node)->dispatch
}

let use = (~clearPolicy: Types.clearPolicy=ClearOnHistoryChanges, ()) => {
  let (state, dispatch) = Context.use()

  // Sets whether clearing state is allowed for the incoming route change
  autoClearRef := clearPolicy === ClearOnHistoryChanges

  // Sets API-returned notification onClose action callback
  let make: Types.notificationRaw => Types.notification = raw => {
    id: raw.id,
    content: raw.content,
    onClose: () => RemoveRequested(raw.id)->dispatch,
  }

  {
    get: state->List.map(make),
    add: (action, ~details=?, ()) => dispatch->add(~action, ~details?, ()),
    reset: (action, ~details=?, ()) => dispatch->add(~action, ~details?, ~reset=true, ()),
    clear: () => ClearRequested->dispatch,
  }
}

module Banner = {
  open StyleX

  @react.component
  let make = (~notifier) =>
    <View style={StyleX.props([style(~width="100%", ())])}>
      {notifier.get
      ->List.map(({id, content, onClose}) => {
        let textStatus = switch content.kind {
        | #success => Banner.Success(content.message)
        | #error => Danger(content.message)
        | #warning => Warning(content.message)
        }

        <Box spaceTop=#medium>
          <Banner
            key={id->Uuid.toString} details=?content.details onRequestClose=onClose textStatus
          />
        </Box>
      })
      ->List.toArray
      ->React.array}
    </View>

  let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
    oldProps.notifier.get === newProps.notifier.get
  )
}
