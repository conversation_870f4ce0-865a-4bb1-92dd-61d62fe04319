// NOTE - to complete generated classes identifiers you have to manually
// add the submodule name to your style fields names. Advised convention:
// ```rescript
// module MySubModule = {
//  let styles = StyleX.create({
//    "MySubModule_root": StyleX.style(...
// ```

module Style = {
  @genType type t
}

let toPx = value => value->Float.toString ++ "px"

type color = string

type size = string

type radius = string

type visibility = [#auto | #hidden]

type margin = size

type font = string

type fontFamily = string

type resizeMode = [#cover | #contain | #stretch | #repeat | #center]

type fontStyle = [#normal | #italic]

type fontWeight = [#normal | #bold | #100 | #200 | #300 | #400 | #500 | #600 | #700 | #800 | #900]

type textAlign = [#auto | #left | #right | #center | #justify]

type textAlignVertical = [#auto | #top | #bottom | #center]

type textDecorationLine = [
  | #none
  | #underline
  | #"line-through"
  | #"underline line-through"
]

type textDecorationStyle = [#solid | #double | #dotted | #dashed]

type textOverflow = [#ellipsis | #clip]

type textTransform = [#none | #uppercase | #lowercase | #capitalize]

type userSelect = [#none | #auto | #text]

type writingDirection = [#auto | #ltr | #rtl]

type whiteSpace = [#normal | #nowrap | #pre | #"pre-wrap" | #"pre-line" | #"break-spaces"]

type wordBreak = [#normal | #"break-word" | #"break-all"]

type backfaceVisibility = [#visible | #hidden]

type borderStyle = [#solid | #dotted | #dashed]

type cursor = [#auto | #pointer | #text | #grab | #move | #wait | #help | #default]

type display = [
  | #none
  | #block
  | #flex
  | #inline
  | #"inline-block"
  | #"inline-flex"
  | #"inline-grid"
  | #"-webkit-box"
  | #"-webkit-inline-box"
  | #contents
]

type overflow = [#auto | #visible | #hidden | #scroll]

type overflowWrap = [#normal | #anywhere | #"break-word"]

type flexWrap = [#wrap | #nowrap]

type position = [#fixed | #absolute | #relative | #sticky]

type alignContent = [
  | #"flex-start"
  | #"flex-end"
  | #center
  | #stretch
  | #"space-around"
  | #"space-between"
]

@genType
type alignItems = [
  | #"flex-start"
  | #"flex-end"
  | #center
  | #stretch
  | #baseline
]

type alignSelf = [
  | #auto
  | #"flex-start"
  | #"flex-end"
  | #center
  | #stretch
  | #baseline
]

type boxOrient = [#vertical]

@genType type direction = [#inherit | #ltr | #rtl]

type flexDirection = [
  | #row
  | #"row-reverse"
  | #column
  | #"column-reverse"
  | #inherit
]

type justifyContent = [
  | #"flex-start"
  | #"flex-end"
  | #center
  | #"space-around"
  | #"space-between"
  | #"space-evenly"
]

type objectFit = [
  | #cover
  | #contain
  | #fill
  | #"scale-down"
]

type placeContent = [
  | #"flex-start"
  | #"flex-end"
  | #center
  | #"space-around"
  | #"space-between"
  | #"space-evenly"
]

type verticalAlign = [
  | #auto
  | #top
  | #bottom
  | #middle
]

type borderCurve = [
  | #circular
  | #continuous
]

type scrollSnapAlign = [#start | #center | #end]
type scrollSnapType = [
  | #none
  | #inline
  | #block
  | #both
  | #"inline mandatory"
  | #"block proximity"
  | #"both mandatory"
]

type pointerEvents = [#auto | #none | #stroke | #fill]

type positionArea = [#left | #center | #right | #bottom | #top]

type boxSizing = [#"border-box" | #"content-box"]

// NOTE — The style definition is incomplete.
// NOTE - This @obj decorator is deprecated in favor of records with
// optionals fields now supported. However at the moment the labelized
// functions arguments benefit from a better auto completion covering.
@obj
external style: (
  // Image style props:
  ~resizeMode: resizeMode=?,
  ~overlayColor: color=?,
  ~tintColor: color=?,
  ~objectFit: objectFit=?,
  // Text style props:
  ~color: color=?,
  ~font: font=?,
  ~fontFamily: fontFamily=?,
  ~fontSize: string=?,
  ~fontStyle: fontStyle=?,
  ~fontWeight: fontWeight=?,
  ~includeFontPadding: bool=?,
  ~letterSpacing: string=?,
  ~lineHeight: string=?,
  ~textAlign: textAlign=?,
  ~textAlignVertical: textAlignVertical=?,
  ~textDecorationColor: color=?,
  ~textDecorationLine: textDecorationLine=?,
  ~textDecorationStyle: textDecorationStyle=?,
  ~textOverflow: textOverflow=?,
  ~textTransform: textTransform=?,
  ~userSelect: userSelect=?,
  ~verticalAlign: verticalAlign=?,
  ~writingDirection: writingDirection=?,
  ~whiteSpace: whiteSpace=?,
  ~wordBreak: wordBreak=?,
  // View styles:
  ~backfaceVisibility: backfaceVisibility=?,
  ~background: string=?,
  ~backgroundImage: string=?,
  ~backgroundColor: color=?,
  ~backgroundImage: string=?,
  ~backgroundPosition: string=?,
  ~backgroundSize: string=?,
  ~boxSizing: boxSizing=?,
  ~border: string=?,
  ~borderBlockColor: color=?,
  ~borderBlockEndColor: color=?,
  ~borderBlockStartColor: color=?,
  ~borderBottom: string=?,
  ~borderBottomColor: color=?,
  ~borderBottomEndRadius: radius=?,
  ~borderBottomLeftRadius: radius=?,
  ~borderBottomRightRadius: radius=?,
  ~borderBottomStartRadius: radius=?,
  ~borderBottomWidth: size=?,
  ~borderColor: color=?,
  ~borderCurve: borderCurve=?,
  ~borderEndColor: color=?,
  ~borderEndEndRadius: radius=?,
  ~borderEndStartRadius: radius=?,
  ~borderEndWidth: size=?,
  ~borderLeft: string=?,
  ~borderLeftColor: color=?,
  ~borderLeftWidth: size=?,
  ~borderRadius: radius=?,
  ~borderRight: string=?,
  ~borderRightColor: color=?,
  ~borderRightWidth: size=?,
  ~borderStartColor: color=?,
  ~borderStartEndRadius: radius=?,
  ~borderStartStartRadius: radius=?,
  ~borderStartWidth: size=?,
  ~borderStyle: borderStyle=?,
  ~borderTop: string=?,
  ~borderTopColor: color=?,
  ~borderTopEndRadius: radius=?,
  ~borderTopLeftRadius: radius=?,
  ~borderTopRightRadius: radius=?,
  ~borderTopStartRadius: radius=?,
  ~borderTopWidth: size=?,
  ~borderWidth: size=?,
  ~\"WebkitBoxOrient": boxOrient=?,
  ~cursor: cursor=?,
  ~elevation: string=?,
  ~filter: string=?,
  ~\"WebkitLineClamp": string=?,
  ~opacity: float=?,
  ~pointerEvents: pointerEvents=?,
  ~positionArea: positionArea=?,
  ~outline: string=?,
  ~outlineColor: string=?,
  ~outlineOffset: string=?,
  ~outlineWidth: string=?,
  // Transition props:
  ~transition: string=?,
  // Transform props:
  ~transform: string=?,
  // Shadow props:
  ~boxShadow: string=?,
  ~visibility: visibility=?,
  // Scroll props:
  ~scrollbarWidth: string=?,
  ~scrollSnapAlign: scrollSnapAlign=?,
  ~scrollSnapType: scrollSnapType=?,
  // Layout style props:
  ~alignContent: alignContent=?,
  ~alignItems: alignItems=?,
  ~alignSelf: alignSelf=?,
  ~aspectRatio: string=?,
  ~bottom: size=?,
  ~columnGap: string=?,
  ~content: string=?,
  ~direction: direction=?,
  ~display: display=?,
  ~end: size=?,
  ~flex: string=?,
  ~flexBasis: string=?,
  ~flexDirection: flexDirection=?,
  ~flexGrow: string=?,
  ~flexShrink: string=?,
  ~flexWrap: flexWrap=?,
  ~gap: string=?,
  ~gridArea: string=?,
  ~gridTemplateAreas: string=?,
  ~gridTemplateColumns: string=?,
  ~height: size=?,
  ~justifyContent: justifyContent=?,
  ~left: size=?,
  ~margin: margin=?,
  ~marginBlock: size=?,
  ~marginBlockEnd: size=?,
  ~marginBlockStart: size=?,
  ~marginBottom: margin=?,
  ~marginEnd: margin=?,
  ~marginHorizontal: margin=?,
  ~marginInline: size=?,
  ~marginInlineEnd: size=?,
  ~marginInlineStart: size=?,
  ~marginLeft: margin=?,
  ~marginRight: margin=?,
  ~marginStart: margin=?,
  ~marginTop: margin=?,
  ~marginVertical: margin=?,
  ~maxHeight: size=?,
  ~maxWidth: size=?,
  ~minHeight: size=?,
  ~minWidth: size=?,
  ~overflow: overflow=?,
  ~overflowX: overflow=?,
  ~overflowY: overflow=?,
  ~overflowWrap: overflowWrap=?,
  ~padding: size=?,
  ~paddingBlock: size=?,
  ~paddingBlockEnd: size=?,
  ~paddingBlockStart: size=?,
  ~paddingBottom: size=?,
  ~paddingEnd: size=?,
  ~paddingInline: size=?,
  ~paddingInlineEnd: size=?,
  ~paddingInlineStart: size=?,
  ~paddingLeft: size=?,
  ~paddingRight: size=?,
  ~paddingStart: size=?,
  ~paddingTop: size=?,
  ~paddingBlock: size=?,
  ~placeContent: placeContent=?,
  ~position: position=?,
  ~right: size=?,
  ~rowGap: string=?,
  ~start: size=?,
  ~top: size=?,
  ~width: size=?,
  ~zIndex: int=?,
  // Pseudo classes:
  ~\":hover": Style.t=?,
  ~\":focus": Style.t=?,
  unit,
) => Style.t = ""

// NOTE - use `style()` to handle optional cases
external arrayStyle: array<Style.t> => Style.t = "%identity"

/*
 StyleX handles @ rules like @media or @container.
 To use them in a typesafe way, you need to use the `atRulesStyle`
 identity function, as in the example below:

 ```rescript
 let styles = StyleX.create({
   "default": {
     color: "black",
   },
   "responsive": {
     "@media screen and (min-width: 900px)": {
       color: "red"
     },
   },
 })
 @react.component
 let make = (~children) => {
   let {?style, ?className} = StyleX.props([styles["default"], atRulesStyle(styles["responsive"])])
   <span ?style ?className> {children} </span>
 }
 ```
 */
external atRulesStyle: {..} => Style.t = "%identity"

// TODO — With ReScript v12, it will be possible to use a type Dict.t<style>
// to have better type safety. For the moment, we're forced to use {..}.
@module("@stylexjs/stylex")
external create: {..} => {..} = "create"

type props = {style?: ReactDOMStyle.t, className?: string}

@module("@stylexjs/stylex") @variadic
external props: array<Style.t> => props = "props"

@module("@stylexjs/stylex") @variadic
external props2: array<Style.t> => JsxDOM.domProps = "props"
