open Intl
open StyleX

module LoginFormLenses = %lenses(
  type state = {
    username: string,
    password: string,
    keepSigned: bool,
  }
)

module LoginForm = Form.Make(LoginFormLenses)

module SignInRequest = {
  let encodeBody = (~username, ~password) =>
    Js.Dict.fromArray([
      ("username", username->Json.encodeString),
      ("password", password->Json.encodeString),
    ])
    ->Json.encodeDict
    ->Json.stringify
    ->Fetch.Body.string

  let decodeResult = json => json->Json.decodeDict->Json.flatDecodeDictFieldString("access_token")

  type makeT = (~username: string, ~password: string) => Future.t<result<string, unit>>

  let make: makeT = (~username, ~password) =>
    Fetch.make(
      Env.gatewayUrl() ++ "/sign-in",
      {
        method: #POST,
        body: encodeBody(~username, ~password),
        headers: Fetch.Headers.fromObject({"Content-Type": "application/json"}),
      },
    )
    ->Promise.then(Fetch.Response.json)
    ->Promise.then(json => Promise.resolve(decodeResult(json)))
    ->FuturePromise.fromPromise
    ->Future.map(result =>
      switch result {
      | Ok(Some(jwt)) => Ok(jwt)
      | Ok(None) | Error(_) => Error()
      }
    )
}

let signInRequest = SignInRequest.make

module RecoveryLinkButton = {
  let styles = StyleX.create({
    "root": StyleX.style(
      ~font=`normal 400 13px "Libre Franklin"`,
      ~color=Colors.neutralColor70,
      ~textDecorationLine=#underline,
      (),
    ),
  })
  let styleProps = () => StyleX.props([styles["root"]])

  @react.component
  let make = (~recoveryRoute) => {
    let {?style, ?className} = styleProps()
    <Navigation.Link to=Route(recoveryRoute)>
      <span ?style ?className> {t("Forgot password ?")->React.string} </span>
    </Navigation.Link>
  }
}

module NotificationBanner = {
  @react.component
  let make = (~notification, ~onRequestClose) =>
    <Box spaceTop=#medium style={StyleX.style(~width="100%", ())}>
      <Banner textStatus=notification onRequestClose />
    </Box>

  let make = React.memo(make)
}

let styles = StyleX.create({
  "container": style(
    ~display=#flex,
    ~flexDirection=#column,
    ~justifyContent=#center,
    ~alignItems=#stretch,
    ~flex="1",
    ~width="465px",
    ~marginTop="-10%",
    (),
  ),
  "title": style(
    ~width="100%",
    ~display=#flex,
    ~flexDirection=#column,
    ~alignItems=#center,
    ~paddingBlock=Spaces.largePx,
    (),
  ),
})

let noop = _ => ()

let schema = [LoginForm.Schema.Email(Username), StringMin(Password, 3)]

@react.component
let make = (
  ~signInRequest: SignInRequest.makeT,
  ~initialNotification=?,
  ~recoveryRoute,
  ~initialUsername="",
) => {
  let authState = Auth.useState()
  let logUser = Auth.useLogUser()

  let (notification, setNotfication) = React.useState(() => initialNotification)

  let onSubmitSuccess = jwt =>
    switch jwt {
    | Some(jwt) => logUser(jwt)
    | _ => setNotfication(_ => Some(Banner.Danger(t("Something went wrong"))))
    }

  let onSubmitFailure = error => setNotfication(_ => Some(Banner.Danger(error)))

  let formPropState = LoginForm.useFormPropState({
    initialValues: {username: initialUsername, password: "", keepSigned: true},
    schema,
    onSubmitSuccess,
    onSubmitFailure,
  })

  let onSubmit = (_, {LoginFormLenses.username: username, password}) =>
    signInRequest(~username, ~password)
    ->Future.mapOk(jwt => Some(jwt))
    ->Future.mapError(_ => t("Incorrect credentials."))

  let onRequestCloseNotificationBanner = () => setNotfication(_ => None)

  <View style={StyleX.props([styles["container"]])}>
    <View style={StyleX.props([styles["title"]])}>
      <Title level=#1> {t("Good to see you again !")->React.string} </Title>
      {switch notification {
      | Some(notification) =>
        <NotificationBanner notification onRequestClose=onRequestCloseNotificationBanner />
      | None => React.null
      }}
    </View>
    <LoginForm.FormProvider propState=formPropState>
      <LoginForm.ControlEnterKey onSubmit />
      <Stack space=#large>
        <LoginForm.InputText
          field=Username
          label={t("Email")}
          placeholder={t("<EMAIL>")}
          hideRequired=true
        />
        <LoginForm.InputPassword
          field=Password
          label={t("Password")}
          placeholder={t("Password")}
          hideError=true
          hideRequired=true
        />
        <Box spaceY=#xsmall>
          {switch authState {
          | Unlogged => <LoginForm.SubmitButton text={t("Sign in")} onSubmit />
          | Logging(_) | Logged(_) =>
            <Button variation=#primary size=#large disabled=true onPress={noop}>
              {(t("Sign in") ++ "...")->React.string}
            </Button>
          }}
        </Box>
      </Stack>
      <RecoveryLinkButton recoveryRoute />
    </LoginForm.FormProvider>
  </View>
}
