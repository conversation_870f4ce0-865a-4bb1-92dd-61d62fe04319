open Intl
open StyleX

let {
  recoveryRoute,
  loginSessionFromWebsiteRoute,
  loginRecoveryEmailSentRoute,
  loginRecoverySuccessRoute,
  logoutImpersonationFailureRoute,
  loginRoute,
} = module(AuthRoutes)

let styles = StyleX.create({
  "container": style(
    ~flex="1",
    ~display=#flex,
    ~flexDirection=#column,
    ~alignItems=#center,
    ~backgroundColor=Colors.backgroundDefaultColortemplate,
    (),
  ),
})

@react.component
let make = (~subUrlPath, ~appIndexRoute) => {
  let {signInRequest} = module(AuthLoginPage)
  <View style={StyleX.props([styles["container"]])}>
    {switch subUrlPath {
    | list{"signup"} =>
      let {signupRequest} = module(AuthSignupPage)
      <AuthSignupPage signupRequest ipAddressRequest=IpAddress.request />
    | list{"signup", "success"} => <AuthSignupSuccessPage />
    | list{"login"} => <AuthLoginPage signInRequest recoveryRoute />
    | list{"login", "from-website"} =>
      <AuthLoginPage
        signInRequest
        initialNotification=Banner.Info(
          template(
            t("You can access the old dashboard [dashboard.wino.fr]({{link}})"),
            ~values={"link": "https://dashboard.wino.fr"},
            (),
          ),
        )
        recoveryRoute
      />
    | list{"login", "session-expired"} =>
      <AuthLoginPage
        signInRequest
        initialNotification=Banner.Info(t("Oops! It seems your session has expired."))
        recoveryRoute
      />
    | list{"login", "recovery-email-sent"} =>
      <AuthLoginPage
        signInRequest
        initialNotification=Banner.Success(
          t("You should have received the recovery link by the next minutes."),
        )
        recoveryRoute
      />
    | list{"login", "password-reset"} =>
      <AuthLoginPage
        signInRequest
        initialNotification=Banner.Success(
          t("Your password has been reset successfully. Please log in"),
        )
        recoveryRoute
      />
    | list{"login", "impersonation-failure"} =>
      <AuthLoginPage
        signInRequest
        initialNotification=Banner.Danger(t("Oops! It seems the impersonation has failed."))
        recoveryRoute
      />
    | list{"login", "username-updated", username} =>
      <AuthLoginPage
        signInRequest
        initialNotification=Banner.Success(t("Your email update has been completed successfully."))
        recoveryRoute
        initialUsername=username
      />
    | list{"logout"}
    | list{"logout", "session-expired" | "impersonation-failure"} =>
      <AuthLogoutPage />
    | list{"recovery"} =>
      let {passwordRecoveryRequest} = module(AuthLoginRecoveryPage)
      <AuthLoginRecoveryPage loginRecoveryEmailSentRoute passwordRecoveryRequest />
    | list{"reset", userId, tokenId, token} =>
      let {resetPasswordRequest} = module(AuthLoginResetPage)
      <AuthLoginResetPage
        userId tokenId token successRoute=loginRecoverySuccessRoute resetPasswordRequest
      />
    | list{"impersonation", userId} =>
      let authLogoutImpersonationFailureRoute = logoutImpersonationFailureRoute
      <AuthImpersonationPage userId authLogoutImpersonationFailureRoute appIndexRoute />
    | list{"update-username", userId, tokenId, token, username} =>
      let {usernameUpdateRequest} = module(AuthUsernameUpdatePage)
      <AuthUsernameUpdatePage userId tokenId token username usernameUpdateRequest />
    | _ => <Navigation.Redirect route={loginRoute} />
    }}
  </View>
}
