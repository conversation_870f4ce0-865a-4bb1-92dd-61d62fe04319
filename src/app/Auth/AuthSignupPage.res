open Intl
open StyleX

module SignupFormLenses = %lenses(
  type state = {
    username: string,
    password: string,
    passwordConfirmation: string,
    name: string,
    email: string,
    phoneNumber: string,
    corporateName: string,
    legalRepresentative: string,
    legalForm: string,
    siretNumber: string,
    rcsNumber: string,
    apeNafCode: string,
    vatNumber: string,
    address: string,
    city: string,
    postalCode: string,
    country: CountryCode.t,
    ibanNumber: string,
    shippingAddress: string,
    shippingPostalCode: string,
    shippingCity: string,
    shippingCountry: CountryCode.t,
    sameShippingAddressThanBilling: bool,
  }
)

module SignupForm = Form.Make(SignupFormLenses)

let stripEmptyString = string =>
  switch string {
  | "" => None
  | string => Some(string)
  }

module SignupRequest = {
  let createUserRegistrationPayloadDict = (
    ~username,
    ~password,
    ~name,
    ~email,
    ~phoneNumber,
    ~corporateName,
    ~legalRepresentative,
    ~legalForm,
    ~siretNumber,
    ~rcsNumber,
    ~apeN<PERSON><PERSON><PERSON>,
    ~vatNumber,
    ~address,
    ~postalCode,
    ~city,
    ~country,
    ~ibanNumber,
    ~shippingAddress,
    ~shippingPostalCode,
    ~shippingCity,
    ~shippingCountry,
    ~sameShippingAddressThanBilling,
    ~ipAddress,
    ~userAgent,
    ~acceptedAt,
  ) => {
    let shop = Js.Dict.fromArray([
      ("name", name->Json.encodeString),
      ("email", email->Json.encodeString),
      ("phoneNumber", phoneNumber->Json.encodeString),
      ("corporateName", corporateName->Json.encodeString),
      ("legalRepresentative", legalRepresentative->Json.encodeString),
      ("legalForm", legalForm->Json.encodeString),
      ("taxCountry", country->CountryCode.toIso2String->Json.encodeString),
      ("address", address->Json.encodeString),
      ("city", city->Json.encodeString),
      ("postalCode", postalCode->Json.encodeString),
      ("country", country->CountryCode.toMediumCountryString->Json.encodeString),
      (
        "billingAccount",
        Js.Dict.fromArray([
          ("corporateName", corporateName->Json.encodeString),
          ("shopName", name->Json.encodeString),
          ("phone", phoneNumber->Json.encodeString),
          ("email", email->Json.encodeString),
          ("vatNumber", vatNumber->Option.getWithDefault("")->Json.encodeString),
          ("ibanNumber", ibanNumber->Json.encodeString),
          (
            "billingAddress",
            Js.Dict.fromArray([
              ("address", address->Json.encodeString),
              ("city", city->Json.encodeString),
              ("postalCode", postalCode->Json.encodeString),
              ("country", country->CountryCode.toIsoString->Json.encodeString),
            ])->Json.encodeDict,
          ),
          (
            "shippingAddress",
            if sameShippingAddressThanBilling {
              Js.Dict.fromArray([
                ("address", address->Json.encodeString),
                ("city", city->Json.encodeString),
                ("postalCode", postalCode->Json.encodeString),
                ("country", country->CountryCode.toIsoString->Json.encodeString),
              ])->Json.encodeDict
            } else {
              Js.Dict.fromArray([
                ("address", shippingAddress->Json.encodeString),
                ("city", shippingCity->Json.encodeString),
                ("postalCode", shippingPostalCode->Json.encodeString),
                ("country", shippingCountry->CountryCode.toIsoString->Json.encodeString),
              ])->Json.encodeDict
            },
          ),
          (
            "sepaMandateAcceptanceDetails",
            Js.Dict.fromArray([
              ("ipAddress", ipAddress->Json.encodeString),
              ("userAgent", userAgent->Json.encodeString),
              ("acceptedAt", acceptedAt->Json.encodeNumber),
            ])->Json.encodeDict,
          ),
        ])->Json.encodeDict,
      ),
    ])

    switch siretNumber {
    | Some(siretNumber) => Js.Dict.set(shop, "siretNumber", Json.encodeString(siretNumber))
    | None => ()
    }

    switch rcsNumber {
    | Some(rcsNumber) => Js.Dict.set(shop, "rcsNumber", Json.encodeString(rcsNumber))
    | None => ()
    }

    switch apeNafCode {
    | Some(apeNafCode) => Js.Dict.set(shop, "apeNafCode", Json.encodeString(apeNafCode))
    | None => ()
    }

    switch vatNumber {
    | Some(vatNumber) => Js.Dict.set(shop, "vatNumber", Json.encodeString(vatNumber))
    | None => ()
    }

    Js.Dict.fromArray([
      ("username", username->Json.encodeString),
      ("password", password->Json.encodeString),
      ("shop", shop->Json.encodeDict),
    ])
  }

  type serverFailure =
    | DuplicateUserUsername
    | InvalidVatNumber
    | WrongSepaMandateAcceptanceDetails
    | InvalidIban
    | InvalidCorporateName
    | IpAddressFailure
    | Unknown

  let decodeInvalidRequestFailure = serverFailure =>
    switch serverFailure {
    | {Request.kind: "DuplicateUserUsername"} => DuplicateUserUsername
    | {kind: "InvalidVatNumber"} => InvalidVatNumber
    | {kind: "WrongSepaMandateAcceptanceDetails"} => WrongSepaMandateAcceptanceDetails
    | {kind: "InvalidCorporateName"} => InvalidCorporateName
    | {kind: "InvalidIban"} => InvalidIban
    | _ => Unknown
    }

  let endpoint = Env.gatewayUrl() ++ "/sign-up"

  type makeT = (
    ~username: string,
    ~password: string,
    ~name: string,
    ~email: string,
    ~phoneNumber: string,
    ~corporateName: string,
    ~legalRepresentative: string,
    ~legalForm: string,
    ~siretNumber: string,
    ~rcsNumber: string,
    ~apeNafCode: string,
    ~vatNumber: string,
    ~address: string,
    ~postalCode: string,
    ~city: string,
    ~country: CountryCode.t,
    ~ibanNumber: string,
    ~shippingAddress: string,
    ~shippingPostalCode: string,
    ~shippingCity: string,
    ~shippingCountry: CountryCode.t,
    ~sameShippingAddressThanBilling: bool,
    ~ipAddress: string,
    ~userAgent: string,
    ~acceptedAt: float,
  ) => Future.t<result<unit, option<serverFailure>>>

  let make: makeT = (
    ~username,
    ~password,
    ~name,
    ~email,
    ~phoneNumber,
    ~corporateName,
    ~legalRepresentative,
    ~legalForm,
    ~siretNumber,
    ~rcsNumber,
    ~apeNafCode,
    ~vatNumber,
    ~address,
    ~postalCode,
    ~city,
    ~country,
    ~ibanNumber,
    ~shippingAddress,
    ~shippingPostalCode,
    ~shippingCity,
    ~shippingCountry,
    ~sameShippingAddressThanBilling,
    ~ipAddress,
    ~userAgent,
    ~acceptedAt,
  ) =>
    Request.make(
      endpoint,
      ~method=#POST,
      ~bodyJson=createUserRegistrationPayloadDict(
        ~username,
        ~password,
        ~name,
        ~email,
        ~phoneNumber,
        ~corporateName,
        ~legalRepresentative,
        ~legalForm,
        ~siretNumber=siretNumber->stripEmptyString,
        ~rcsNumber=rcsNumber->stripEmptyString,
        ~apeNafCode=apeNafCode->stripEmptyString,
        ~vatNumber=vatNumber->stripEmptyString,
        ~address,
        ~postalCode,
        ~city,
        ~country,
        ~ibanNumber,
        ~shippingAddress,
        ~shippingPostalCode,
        ~shippingCity,
        ~shippingCountry,
        ~sameShippingAddressThanBilling,
        ~ipAddress,
        ~userAgent,
        ~acceptedAt,
      )->Json.encodeDict,
      ~authTokenRequired=false,
    )
    ->Future.mapOk(_ => ())
    ->Future.mapError(error =>
      switch error {
      | InvalidRequestFailures(invalidRequestFailures) =>
        invalidRequestFailures->Array.get(0)->Option.map(decodeInvalidRequestFailure)
      | _ => None
      }
    )
}

let signupRequest = SignupRequest.make

module NotificationBanner = {
  @react.component
  let make = (~notification) =>
    <Box spaceTop=#medium>
      <Banner textStatus=notification />
    </Box>

  let make = React.memo(make)
}

let validateAddressValue = (value, values: SignupFormLenses.state) => {
  if !values.sameShippingAddressThanBilling && value === "" {
    Error(t("Please fulfill this field."))
  } else {
    Ok()
  }
}

let schema = [
  SignupForm.Schema.Email(Username),
  Password(Password),
  StringNotEmpty(PasswordConfirmation),
  CustomString(
    PasswordConfirmation,
    (value, values) => {
      if values.password === value {
        Ok()
      } else {
        Error(t("Password and its confirmation must be identical"))
      }
    },
  ),
  StringNotEmpty(Name),
  Email(Email),
  PhoneNumber(PhoneNumber),
  CustomString(
    CorporateName,
    (value, _) => {
      if value->CorporateEntity.CorporateName.validate {
        Ok()
      } else {
        Error(t("The corporate name is invalid. Please provide the full name of the company."))
      }
    },
  ),
  StringNotEmpty(Address),
  StringNotEmpty(PostalCode),
  StringNotEmpty(City),
  CustomString(ShippingAddress, validateAddressValue),
  CustomString(ShippingPostalCode, validateAddressValue),
  CustomString(ShippingCity, validateAddressValue),
  StringNotEmpty(IbanNumber),
  CustomString(
    IbanNumber,
    (value, _) => {
      if value->CorporateEntity.Iban.validate {
        Ok()
      } else {
        Error(t("The IBAN number is not valid."))
      }
    },
  ),
  CustomString(
    LegalRepresentative,
    (value, _) => {
      if value->String.length < 3 {
        Error(t("The legal representative is not valid, full name must be given."))
      } else {
        Ok()
      }
    },
  ),
]

module SignUpHeader = {
  @module("./wino_logo.png") external imageUri: string = "default"

  let imageSize = "40px"
  let styles = StyleX.create({
    "image": style(~height=imageSize, ~width=imageSize, ~alignSelf=#center, ()),
  })

  @react.component
  let make = (~onSubmit) =>
    <Inline align=#spaceBetween>
      <Inline space=#medium alignY=#center>
        <img {...StyleX.props2([styles["image"]])} src={imageUri} />
        <Title level=#1> {t("Set Up Your Wino Account")->React.string} </Title>
      </Inline>
      <SignupForm.SubmitButton
        text={t("Create your account")} onSubmit variation={#success} size={#large}
      />
    </Inline>
}

let styles = StyleX.create({
  "container": style(
    ~maxWidth="1480px",
    ~paddingTop=Spaces.xlargePx,
    ~paddingBottom=Spaces.xxhugePx,
    ~paddingInline=Spaces.xxlargePx,
    (),
  ),
})

@react.component
let make = (
  ~signupRequest: SignupRequest.makeT,
  ~ipAddressRequest,
  ~initialNotification=?,
  ~initialUsername="",
) => {
  let navigate = Navigation.useNavigate()

  let (notification, setNotfication) = React.useState(() => initialNotification)

  let onSubmitSuccess = _ => navigate(AuthRoutes.signupSuccess)
  let onSubmitFailure = error => {
    WebAPI.window
    ->WebAPI.Window.scrollToWithOptions({
      "top": 0.,
      "left": 0.,
      "behavior": "smooth",
    })
    ->ignore
    setNotfication(_ => Some(Banner.Danger(error)))
  }

  let formPropState = SignupForm.useFormPropState({
    initialValues: {
      username: initialUsername,
      password: "",
      passwordConfirmation: "",
      name: "",
      email: "",
      phoneNumber: "",
      corporateName: "",
      legalRepresentative: "",
      legalForm: "",
      siretNumber: "",
      rcsNumber: "",
      apeNafCode: "",
      vatNumber: "",
      address: "",
      city: "",
      postalCode: "",
      country: FR,
      ibanNumber: "",
      shippingAddress: "",
      shippingPostalCode: "",
      shippingCity: "",
      shippingCountry: FR,
      sameShippingAddressThanBilling: true,
    },
    schema,
    onSubmitSuccess,
    onSubmitFailure,
  })

  let (formState, formDispatch) = formPropState
  let {values, submission, validation} = formState

  let submitting = submission->Form.Submission.isRequested

  React.useEffect1(() => {
    if submitting {
      setNotfication(_ => None)
    }
    None
  }, [submitting])

  let onSubmit = (
    _,
    {
      SignupFormLenses.username: username,
      password,
      name,
      email,
      phoneNumber,
      corporateName,
      legalRepresentative,
      legalForm,
      siretNumber,
      rcsNumber,
      apeNafCode,
      vatNumber,
      address,
      city,
      postalCode,
      country,
      ibanNumber,
      shippingAddress,
      shippingPostalCode,
      shippingCity,
      shippingCountry,
      sameShippingAddressThanBilling,
    },
  ) =>
    ipAddressRequest()
    ->Future.tapError(error => {
      BugTracker.reportErrorMessage(
        "Fetching IP on signup failed with error: " ++
        Json.stringifyAny(error)->Option.getWithDefault("unknown"),
      )
    })
    ->Future.flatMap(ipRequestResult =>
      switch ipRequestResult {
      | Ok(ipAddress) =>
        signupRequest(
          ~username,
          ~password,
          ~name,
          ~email,
          ~phoneNumber,
          ~corporateName,
          ~legalRepresentative,
          ~legalForm,
          ~siretNumber,
          ~rcsNumber,
          ~apeNafCode,
          ~vatNumber,
          ~address,
          ~postalCode,
          ~city,
          ~country,
          ~ibanNumber=ibanNumber->CorporateEntity.Iban.sanitize,
          ~shippingAddress,
          ~shippingPostalCode,
          ~shippingCity,
          ~shippingCountry,
          ~sameShippingAddressThanBilling,
          ~acceptedAt=Js.Date.now(),
          ~ipAddress,
          ~userAgent=UserAgent.get(),
        )
      | Error() => Future.value(Error(Some(SignupRequest.IpAddressFailure)))
      }
    )
    ->Future.mapOk(_ => Some(t("Account successfully set up.")))
    ->Future.mapError(failure =>
      switch failure {
      | Some(SignupRequest.DuplicateUserUsername) => t("This email address is already used.")
      | Some(InvalidVatNumber) => t("The VAT number is not valid.")
      | Some(InvalidIban) => t("The IBAN number is not valid.")
      | Some(WrongSepaMandateAcceptanceDetails) =>
        let text = "Your internet connection has been interrupted, please reload the page before submitting the form again."
        t(text)
      | Some(InvalidCorporateName) =>
        t("The corporate name is not valid, you must enter the name of your company.")
      | _ => t("An unexpected error occured. Please try again or contact the support.")
      }
    )

  let errorMessageAddress = switch (validation, submission) {
  | (Error(errors), Failed(_)) =>
    errors
    ->Array.keepMap(((field, error)) =>
      switch (field, error) {
      | (SignupForm.Schema.Field(SignupFormLenses.Address), error) => Some(error)
      | _ => None
      }
    )
    ->Array.get(0)
  | _ => None
  }
  let onChangeAddress = value => formDispatch(FieldValueChanged(Address, _ => value))
  let onRequestAddressAutoComplete = (address: AddressComboBoxField.address) => {
    formDispatch(FieldValueChanged(Address, _ => address.name))
    formDispatch(FieldValueChanged(PostalCode, _ => address.postcode))
    formDispatch(FieldValueChanged(City, _ => address.city))
  }

  let errorMessageShippingAddress = switch (validation, submission) {
  | (Error(errors), Failed(_)) =>
    errors
    ->Array.keepMap(((field, error)) =>
      switch (field, error) {
      | (SignupForm.Schema.Field(SignupFormLenses.ShippingAddress), error) => Some(error)
      | _ => None
      }
    )
    ->Array.get(0)
  | _ => None
  }
  let onChangeShippingAddress = value =>
    formDispatch(FieldValueChanged(ShippingAddress, _ => value))
  let onRequestShippingAddressAutoComplete = (address: AddressComboBoxField.address) => {
    formDispatch(FieldValueChanged(ShippingAddress, _ => address.name))
    formDispatch(FieldValueChanged(ShippingPostalCode, _ => address.postcode))
    formDispatch(FieldValueChanged(ShippingCity, _ => address.city))
  }

  let siretLabel = switch values.country {
  | CountryCode.BE => t("BCE number")
  | FR | FR_20R | FR_974 => t("SIRET number")
  | LU => t("N/A")
  }

  let siretPlaceholder = switch values.country {
  | CountryCode.BE => t("Enter 10 digits")
  | FR | FR_20R | FR_974 => t("Enter 14 digits")
  | LU => t("N/A")
  }

  let rcsNumberLabel = switch values.country {
  | CountryCode.LU => t("RCSL number")
  | FR | FR_20R | FR_974 => t("RCS number")
  | BE => t("N/A")
  }

  let rcsNumberPlaceholder = switch values.country {
  | CountryCode.LU => t("B201345")
  | FR | FR_20R | FR_974 => t("RCS Paris B *********")
  | BE => t("N/A")
  }

  <SignupForm.FormProvider propState=formPropState>
    <View style={StyleX.props([styles["container"]])}>
      <SpinnerModal
        title={t("Account creation")}
        opened={submitting}
        loopMessages=[
          t("User account creation"),
          t("Subscription setup"),
          t("Tax creation"),
          t("Configuring devices"),
          t("Validating your payment method"),
        ]
      />
      <SignUpHeader onSubmit />
      {switch notification {
      | Some(notification) => <NotificationBanner notification />
      | None => React.null
      }}
      <Box spaceTop=#huge>
        <SignupForm.ControlEnterKey onSubmit />
        <Stack space=#large>
          <FieldsetLayoutPanel
            title={t("Login details")}
            description={t(
              "Please make a note of your password out of sight. This will solve a potential slight memory lapse when first logging into Wino!",
            )}>
            <SignupForm.InputText
              field=Username label={t("Account email")} placeholder={t("<EMAIL>")}
            />
            <SignupForm.InputPassword
              field=Password
              label={t("Password")}
              placeholder={t("Password")}
              showTypingValidation={true}
            />
            <SignupForm.InputPassword
              field=PasswordConfirmation
              label={t("Password confirmation")}
              placeholder={t("Password confirmation")}
              hideRequired=true
            />
          </FieldsetLayoutPanel>
          <FieldsetLayoutPanel
            title={t("General store information.")}
            description={t(
              "Complete the store name and your contact information that will be displayed on your sales documents.",
            )}>
            <SignupForm.InputText field=Name label={t("Shop name")} placeholder={t("Shop name")} />
            <SignupForm.InputText
              field=Email label={t("Shop email")} placeholder={t("<EMAIL>")}
            />
            <SignupForm.InputText
              field=PhoneNumber label={t("Shop phone")} placeholder={t("01 23 45 67 89")}
            />
          </FieldsetLayoutPanel>
          <FieldsetLayoutPanel
            title={t("Billing address")}
            description={t(
              "This address will be used for billing our services. It will also appear on your sales documents issued by the Wino software. If needed, it can be changed later within the software.",
            )}>
            <AddressComboBoxField
              required=true
              errorMessage=?errorMessageAddress
              addressName=values.address
              onInputChange=onChangeAddress
              onRequestAutoComplete=onRequestAddressAutoComplete
            />
            <Group>
              <SignupForm.InputText
                field=PostalCode label={t("Postal code")} placeholder={t("Postal code")}
              />
              <SignupForm.InputText field=City label={t("City")} placeholder={t("City")} />
            </Group>
            <SignupForm.InputSelect
              tooltip={<Tooltip.Span
                text={t(
                  "For your country, carefully select the proposal corresponding to your situation. Your choice will determine the VAT settings on your account.",
                )}
              />}
              field=Country
              label={t("Country")}
              sections={[
                {
                  Select.items: CountryCode.values->Array.map(value => {
                    Select.label: t(value->CountryCode.toLongCountryString),
                    key: value->CountryCode.toIso2String,
                    value,
                  }),
                },
              ]}
            />
          </FieldsetLayoutPanel>
          <FieldsetLayoutPanel
            title={t("Shipping address")}
            description={t(
              "Provide the address where you would like to receive your cash register equipment.",
            )}>
            <SignupForm.InputToggleSwitch
              field={SameShippingAddressThanBilling}
              label={t("Shipping address same as billing address.")}
            />
            {if !values.sameShippingAddressThanBilling {
              <Stack space=#large>
                <AddressComboBoxField
                  required=true
                  errorMessage=?errorMessageShippingAddress
                  addressName=values.shippingAddress
                  onInputChange=onChangeShippingAddress
                  onRequestAutoComplete=onRequestShippingAddressAutoComplete
                />
                <Group>
                  <SignupForm.InputText
                    field=ShippingPostalCode label={t("Postal code")} placeholder={t("Postal code")}
                  />
                  <SignupForm.InputText
                    field=ShippingCity label={t("City")} placeholder={t("City")}
                  />
                </Group>
                <SignupForm.InputSelect
                  field=ShippingCountry
                  label={t("Country")}
                  sections={[
                    {
                      Select.items: BillingAccount.acceptedCountryCodes->Array.map(value => {
                        Select.label: t(value->CountryCode.toMediumCountryString),
                        key: value->CountryCode.toIso2String,
                        value,
                      }),
                    },
                  ]}
                />
              </Stack>
            } else {
              React.null
            }}
          </FieldsetLayoutPanel>
          <FieldsetLayoutPanel
            title={t("Company information")}
            description={t(
              "These details will be used for billing our services. They will also appear on your sales documents issued by the Wino software.",
            )}>
            <Group>
              <SignupForm.InputText
                field=CorporateName
                label={t("Corporate name")}
                placeholder={t(
                  "Company name as displayed on your official business registration document.",
                )}
              />
              <SignupForm.InputSuggestionComboBox
                field=LegalForm
                label={t("Legal form")}
                placeholder={t("Legal form")}
                items={CorporateEntity.CompanyLegalForm.values->Array.map(value => {
                  InputSuggestionComboBoxField.value: t(
                    value->CorporateEntity.CompanyLegalForm.toString,
                  ),
                })}
              />
            </Group>
            <SignupForm.InputText
              field=LegalRepresentative
              label={t("Legal representative")}
              placeholder={t("Anna Rossi")}
            />
            {switch values.country {
            | CountryCode.LU =>
              <SignupForm.InputText
                field=RcsNumber label=rcsNumberLabel placeholder=rcsNumberPlaceholder
              />
            | BE =>
              <SignupForm.InputText
                field=SiretNumber label=siretLabel placeholder=siretPlaceholder
              />
            | FR | FR_20R | FR_974 =>
              <Group>
                <SignupForm.InputText
                  field=SiretNumber label=siretLabel placeholder=siretPlaceholder
                />
                <SignupForm.InputText
                  field=RcsNumber label=rcsNumberLabel placeholder=rcsNumberPlaceholder
                />
              </Group>
            }}
            <Group>
              <SignupForm.InputText
                field=VatNumber label={t("VAT number")} placeholder={t("FR 11 *********")}
              />
              <SignupForm.InputText
                field=ApeNafCode
                label={t("APE/NAF code")}
                placeholder={t("Enter a code of 4 digits and one letter")}
              />
            </Group>
          </FieldsetLayoutPanel>
          <FieldsetLayoutPanel
            title={t("Banking details for SEPA direct debit")}
            description={t(
              "Entering your bank details and then validating the form automatically triggers the approval of a SEPA direct debit mandate.",
            )}>
            <SignupForm.InputText
              field=IbanNumber
              label={t("IBAN number")}
              placeholder={t("FR12 1234 5678 1000 0000 0000 123")}
            />
            <Box spaceTop=#medium>
              <Banner
                textStatus={Banner.Info(
                  t(
                    "By providing your IBAN and clicking on 'Create account' from this URL, you authorize Wino Technologies and Stripe, our payment service provider, to send instructions to your bank to debit your account and your bank to debit your account in accordance with these instructions. You are entitled to a refund from your bank under the terms and conditions of the agreement you have entered into with them. The refund must be requested within 8 weeks from the date your account was debited.",
                  ),
                )}
              />
            </Box>
          </FieldsetLayoutPanel>
        </Stack>
      </Box>
      <PageBottomActionsBar
        displayThreshold=180.
        renderEnd={() =>
          <SignupForm.SubmitButton
            text={t("Create your account")} onSubmit variation={#success} size={#large}
          />}
      />
    </View>
  </SignupForm.FormProvider>
}
