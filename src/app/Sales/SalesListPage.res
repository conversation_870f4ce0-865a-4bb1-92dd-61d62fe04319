open Intl
open StyleX

@module("./sales_empty_placeholder.png") external mainPlaceholder: string = "default"

let styles = StyleX.create({
  "image": style(~height="225px", ~width="300px", ~alignSelf=#center, ()),
  "tags": style(~alignSelf=#center, ~paddingTop=Spaces.mediumPx, ()),
  "action": style(~alignSelf=#center, ()),
  "tag": style(~backgroundColor=Colors.neutralColor15, ()),
  "tagText": style(~color=Colors.neutralColor100, ()),
})

module ExportType = {
  type t =
    | All
    | InvoicesAndCreditNotes
    | ReceiptsAndRefunds

  let toLabel = value =>
    switch value {
    | All => t("All")
    | InvoicesAndCreditNotes => t("Invoices & credit notes")
    | ReceiptsAndRefunds => t("Receipts & refunds")
    }

  let toRawString = value =>
    switch value {
    | All => t("ALL")
    | InvoicesAndCreditNotes => "INVOICES_AND_CREDIT_NOTES"
    | ReceiptsAndRefunds => "RECEIPTS"
    }

  let values = [InvoicesAndCreditNotes, ReceiptsAndRefunds]
}

module SalesExportRequest = {
  let endpoint = Env.sheetUrl() ++ "/sales/sales-export"

  let encodeRequestBodyJson = (~shopIds, ~startDate, ~endDate, ~exportType) => {
    Js.Dict.fromArray([
      ("shopIds", shopIds->Array.map(shopId => shopId->Json.encodeString)->Json.encodeArray),
      ("startDate", startDate->Json.encodeNumber),
      ("endDate", endDate->Json.encodeNumber),
      ("timeZone", Intl.timeZone->Json.encodeString),
      ("exportType", exportType->ExportType.toRawString->Json.encodeString),
    ])->Json.encodeDict
  }

  let make = (~shopIds, ~startDate, ~endDate, ~exportType) =>
    Request.make(
      endpoint,
      ~method=#POST,
      ~bodyJson=encodeRequestBodyJson(~shopIds, ~startDate, ~endDate, ~exportType),
    )
}

module SalesExportButton = {
  @react.component
  let make = (~shopIds, ~startDate, ~endDate, ~exportType, ~text, ~request, ~onNotification) => {
    let captureEvent = SessionTracker.useCaptureEvent()

    let request = () => {
      captureEvent(#download_sales_export_file)
      request(~shopIds, ~startDate, ~endDate, ~exportType)
    }

    let onFailure = error => {
      let errorMessage = switch error {
      | RequestOpenStorageUrlButton.RequestError(InvalidRequestFailures([
          {Request.kind: "SalesExportDateRangeFailure"},
        ])) =>
        t("The sales cannot be exported for a period exceeding one year.")
      | _ => error->RequestOpenStorageUrlButton.failureErrorToString(~exportName=t("sales"))
      }
      onNotification(Banner.Danger(errorMessage))
    }

    let onSuccess = () =>
      onNotification(Banner.Success(t("The file has been downloaded successfully.")))

    let operableRequest = Ok(request)

    <RequestOpenStorageUrlButton text operableRequest onFailure onSuccess />
  }
}

@react.component
let make = (~request) => {
  let scope = Auth.useScope()
  let organisationAccount = switch scope {
  | Organisation(_) => true
  | Single(_) => false
  }
  let shopIds = switch scope {
  | Single(shop) | Organisation({activeShop: Some(shop)}) => [shop]
  | Organisation({shops}) => shops
  }->Array.map(shop => shop.id)

  let shopName: option<string> = switch scope {
  | Organisation({activeShop: Some(shop)}) => Some(shop.name)
  | _ => None
  }

  let ((startDate, endDate), setDateRange) = React.useState(_ => {
    let now = Js.Date.fromFloat(Js.Date.now())
    (now->DateHelpers.startOfMonth, now)
  })
  let (formattedStartDate, formattedEndDate) = (
    startDate->Intl.dateTimeFormat(~dateStyle=#short),
    endDate->Intl.dateTimeFormat(~dateStyle=#short),
  )

  let (exportType, setExportType) = React.useState(() => ExportType.All)

  let (notification, setNotfication) = React.useState(() => None)
  let onNotification = message => setNotfication(_ => Some(message))

  let renderHeaderActions = () =>
    <Box spaceBottom=#medium>
      <Inline space=#small>
        {if organisationAccount {
          <Auth.SelectShopFilter />
        } else {
          React.null
        }}
        {if organisationAccount {
          <Separator />
        } else {
          React.null
        }}
        <Select
          grow=false
          preset=#filter
          label={t("Type")}
          sections={
            let items = ExportType.values->Array.map(value => {
              Select.key: value->ExportType.toRawString,
              label: value->ExportType.toLabel,
              value: Some(value),
            })
            [
              {
                Select.items: [
                  {
                    key: "default",
                    label: template(t("All"), ()),
                    value: Some(ExportType.All),
                    sticky: true,
                  },
                ],
              },
              {title: t("Type of sales"), items},
            ]
          }
          value=Some(exportType)
          onChange={value => setExportType(_ => value->Option.getWithDefault(All))}
        />
        <SelectDateRangeFilter
          placeholder={t("Select a period")}
          value=(startDate, endDate)
          disabledResetButton=true
          onChange={dateRange => {
            switch dateRange {
            | Some(start, end) => setDateRange(_ => (start, end))
            | None => ()
            }
          }}
          triggerLabelDisplay=#showPreset
        />
      </Inline>
    </Box>

  <Page title={t("Receipts & Invoices")} renderHeaderActions variation=#compact>
    {switch notification {
    | Some(notification) =>
      <Box spaceBottom=#normal>
        <Banner textStatus=notification />
      </Box>
    | None => React.null
    }}
    <Card grow=true centerContent=true>
      <img {...StyleX.props2([styles["image"]])} src={mainPlaceholder} />
      <Stack space=#small>
        <Title align=#center level=#2>
          {t("Displaying receipts and invoices is not yet available.")->React.string}
        </Title>
        <TextStyle align=#center>
          {t(
            "In the meantime, you can still export them. The file also includes refunds and credits.",
          )->React.string}
        </TextStyle>
        <Stack space=#large>
          <View style={StyleX.props([styles["tags"]])}>
            <Inline space={#small}>
              {switch shopName {
              | Some(name) => <Tag> {name->React.string} </Tag>
              | None => React.null
              }}
              {switch exportType {
              | InvoicesAndCreditNotes => <Tag> {t("Invoices & credit notes")->React.string} </Tag>
              | ReceiptsAndRefunds => <Tag> {t("Receipts & refunds")->React.string} </Tag>
              | All => React.null
              }}
              <Tag>
                {if formattedStartDate === formattedEndDate {
                  template(t("On {{date}}"), ~values={"date": formattedStartDate}, ())->React.string
                } else {
                  template(
                    t("From {{startDate}} to {{endDate}}"),
                    ~values={
                      "startDate": formattedStartDate,
                      "endDate": formattedEndDate,
                    },
                    (),
                  )->React.string
                }}
              </Tag>
            </Inline>
          </View>
          <View style={StyleX.props([styles["action"]])}>
            <SalesExportButton
              startDate={startDate->Js.Date.getTime}
              endDate={endDate->Js.Date.getTime}
              exportType
              request
              shopIds
              onNotification
              text={t("Export the document")}
            />
          </View>
        </Stack>
      </Stack>
    </Card>
  </Page>
}
