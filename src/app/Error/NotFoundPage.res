open Intl
open StyleX

@module("./not_found.png") external imageUri: string = "default"

let styles = StyleX.create({
  "container": style(
    ~flex="1",
    ~display=#flex,
    ~flexDirection=#column,
    ~justifyContent=#center,
    ~alignSelf=#center,
    ~width="450px",
    (),
  ),
  "image": style(
    ~height="186px",
    ~width="544px",
    ~display=#flex,
    ~flexDirection=#column,
    ~alignSelf=#center,
    ~marginTop="-150px",
    ~marginBottom=Spaces.xlargePx,
    (),
  ),
  "action": style(~alignSelf=#center, ~paddingTop=Spaces.largePx, ()),
})

@react.component
let make = () => {
  let (canGoBack, onGoBack) = Navigation.useGoBack()

  <View style={StyleX.props([styles["container"]])}>
    <img {...StyleX.props2([styles["image"]])} src={imageUri} />
    <Stack space=#normal>
      <Title align=#center>
        {t("Ooops ! This page doesn't seem available now.")->React.string}
      </Title>
      <TextStyle align=#center size=#huge>
        {t("Please try refreshing the page.")->React.string}
      </TextStyle>
      <View style={StyleX.props([styles["action"]])}>
        <Button onPress={_ => canGoBack ? onGoBack() : ()}>
          {t("Go back to the previous page")->React.string}
        </Button>
      </View>
    </Stack>
  </View>
}
