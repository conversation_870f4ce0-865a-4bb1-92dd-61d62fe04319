open Intl
open StyleX

@module("./maintenance.png") external imageUri: string = "default"

let styles = StyleX.create({
  "container": style(
    ~flex="1",
    ~display=#flex,
    ~flexDirection=#column,
    ~justifyContent=#center,
    ~alignSelf=#center,
    ~width="450px",
    (),
  ),
  "image": style(
    ~height="186px",
    ~width="544px",
    ~display=#flex,
    ~flexDirection=#column,
    ~alignSelf=#center,
    ~marginTop="-150px",
    ~marginBottom=Spaces.xlargePx,
    (),
  ),
  "action": style(~alignSelf=#center, ~paddingTop=Spaces.largePx, ()),
})

@react.component
let make = () =>
  <View style={StyleX.props([styles["container"]])}>
    <img {...StyleX.props2([styles["image"]])} src={imageUri} />
    <Stack space=#normal>
      <Title align=#center>
        {t("The dashboard is currently unavailable due to a maintenance.")->React.string}
      </Title>
      <TextStyle align=#center size=#huge>
        {t("<PERSON><PERSON> will be back soon, thank you for your patience.")->React.string}
      </TextStyle>
      <View style={StyleX.props([styles["action"]])}>
        <Button onPress={_ => WebAPI.location->WebAPI.Location.reload}>
          {t("Refresh the page")->React.string}
        </Button>
      </View>
    </Stack>
  </View>
