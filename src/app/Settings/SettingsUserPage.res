open Intl

module UpdatePasswordRequest = {
  let endpoint = Env.gatewayUrl() ++ "/auth/password-update"
  let encodeBody = (~currentPassword, ~newPassword) =>
    Js.Dict.fromArray([
      ("currentPassword", currentPassword->Json.encodeString),
      ("newPassword", newPassword->Json.encodeString),
    ])->Json.encodeDict

  type failureKind = WrongUserPassword | UnknownServerFailure

  let decodeInvalidRequestFailure = error =>
    switch error {
    | {Request.kind: "WrongUserPassword"} => WrongUserPassword
    | _ => UnknownServerFailure
    }

  type makeT = (
    ~currentPassword: string,
    ~newPassword: string,
  ) => Future.t<result<Json.t, option<failureKind>>>

  let make: makeT = (~currentPassword, ~newPassword) =>
    Request.make(
      endpoint,
      ~method=#POST,
      ~bodyJson=encodeBody(~currentPassword, ~newPassword),
    )->Future.mapError(error =>
      switch error {
      | InvalidRequestFailures(invalidRequestFailures) =>
        invalidRequestFailures[0]->Option.map(decodeInvalidRequestFailure)
      | _ => None
      }
    )
}
let updatePasswordRequest = UpdatePasswordRequest.make

module EditPasswordFormLenses = %lenses(
  type state = {
    currentPassword: string,
    newPassword: string,
    newPasswordConfirmation: string,
  }
)

module EditPasswordForm = Form.Make(EditPasswordFormLenses)

module EditPasswordFormModal = {
  let schema = [
    EditPasswordForm.Schema.StringNotEmpty(CurrentPassword),
    Password(NewPassword),
    StringNotEmpty(NewPasswordConfirmation),
    CustomString(
      NewPasswordConfirmation,
      (value, values) =>
        if values.newPassword === value {
          Ok()
        } else {
          Error(t("Password and its confirmation must be identical"))
        },
    ),
  ]

  @react.component
  let make = (~onRequestClose, ~opened, ~onNotification, ~updatePasswordRequest) => {
    let (error, setError) = React.useState(_ => None)

    let onSubmit = (_, {EditPasswordFormLenses.currentPassword: currentPassword, newPassword}) =>
      updatePasswordRequest(~currentPassword, ~newPassword)
      ->Future.mapOk(_ => Some(
        t("Your user and account information has been successfully updated."),
      ))
      ->Future.mapError(error =>
        switch error {
        | Some(UpdatePasswordRequest.WrongUserPassword) => t("Wrong user password provided.")
        | Some(UnknownServerFailure) | None =>
          t("An unexpected error occured. Please try again or contact the support.")
        }
      )

    let onSubmitSuccess = _ => {
      let success = t("Your user and account information has been successfully updated.")
      onNotification(Some(Banner.Success(success)))
      onRequestClose()
    }

    let onSubmitFailure = message => setError(_ => Some(message))

    let formPropState = EditPasswordForm.useFormPropState({
      initialValues: {currentPassword: "", newPassword: "", newPasswordConfirmation: ""},
      schema,
      onSubmitSuccess,
      onSubmitFailure,
      resetValuesAfterSubmission: true,
    })

    <Modal title={t("Update password")} hideFooter=true opened onRequestClose>
      <EditPasswordForm.FormProvider propState=formPropState>
        {switch error {
        | Some(message) =>
          <Box spaceTop=#large spaceX=#xlarge>
            <Banner textStatus=Danger(message) onRequestClose={_ => setError(_ => None)} />
          </Box>
        | _ => React.null
        }}
        <Box spaceTop=#xlarge spaceX=#xlarge>
          <StackFields>
            <EditPasswordForm.InputPassword
              field=CurrentPassword
              label={t("Current password")}
              placeholder={t("Enter your current password")}
              hideError=true
              hideRequired=true
            />
            <EditPasswordForm.InputPassword
              field=NewPassword
              label={t("New password")}
              placeholder={t("Enter your new password")}
              hideRequired=true
              showTypingValidation=true
            />
            <EditPasswordForm.InputPassword
              field=NewPasswordConfirmation
              label={t("New password confirmation")}
              placeholder={t("Confirm your new password")}
              hideRequired=true
            />
          </StackFields>
          <Box spaceY=#xlarge>
            <Inline align=#end space=#xmedium>
              <Button variation=#neutral onPress={_ => onRequestClose()}>
                {t("Cancel")->React.string}
              </Button>
              <EditPasswordForm.SubmitButton
                text={t("Save")} size=#normal variation=#success onSubmit
              />
            </Inline>
          </Box>
        </Box>
      </EditPasswordForm.FormProvider>
    </Modal>
  }

  let make = React.memo(make)
}

module UpdateEmailRequest = {
  let encodeBody = (~newUsername, ~password) =>
    Js.Dict.fromArray([
      ("newUsername", newUsername->Json.encodeString),
      ("password", password->Json.encodeString),
    ])->Json.encodeDict

  let endpoint = Env.gatewayUrl() ++ "/auth/username-update"

  type failureKind = DuplicateUserUsername | WrongUserPassword | UnknownServerFailure

  let decodeInvalidRequestFailure = error =>
    switch error {
    | {Request.kind: "DuplicateUserUsername"} => DuplicateUserUsername
    | {kind: "WrongUserPassword"} => WrongUserPassword
    | _ => UnknownServerFailure
    }

  type makeT = (
    ~newUsername: string,
    ~password: string,
  ) => Future.t<result<Json.t, option<failureKind>>>

  let make: makeT = (~newUsername, ~password) => {
    Request.make(
      endpoint,
      ~method=#POST,
      ~bodyJson=encodeBody(~newUsername, ~password),
    )->Future.mapError(error =>
      switch error {
      | InvalidRequestFailures(invalidRequestFailures) =>
        invalidRequestFailures[0]->Option.map(decodeInvalidRequestFailure)
      | _ => None
      }
    )
  }
}
let updateEmailRequest = UpdateEmailRequest.make

module EditEmailFormLenses = %lenses(
  type state = {
    newUsername: string,
    password: string,
  }
)

module EditEmailForm = Form.Make(EditEmailFormLenses)

module EditEmailModal = {
  let schema = [EditEmailForm.Schema.Email(NewUsername), StringNotEmpty(Password)]

  @react.component
  let make = (~onRequestClose, ~opened, ~currentUsername, ~onNotification, ~updateEmailRequest) => {
    let logUser = Auth.useLogUser()
    let jwt = Auth.getJwt()->Option.getWithDefault("")

    let (error, setError) = React.useState(_ => None)

    let onSubmit = (_, {EditEmailFormLenses.newUsername: newUsername, password}) =>
      updateEmailRequest(~newUsername, ~password)
      ->Future.mapOk(_ => Some(t("Your email address has been updated successfully.")))
      ->Future.mapError(failure =>
        switch failure {
        | Some(UpdateEmailRequest.DuplicateUserUsername) => t("This email address is already used.")
        | Some(WrongUserPassword) => t("Wrong user password provided.")
        | _ => t("An unexpected error occured. Please try again or contact the support.")
        }
      )

    let onSubmitSuccess = _ => {
      logUser(jwt)
      onNotification(
        Some(
          Banner.Warning(
            t(
              "You will receive a link by email to confirm the email address change within a few minutes.",
            ),
          ),
        ),
      )
      onRequestClose()
    }

    let onSubmitFailure = message => setError(_ => Some(message))

    let formPropState = EditEmailForm.useFormPropState({
      initialValues: {newUsername: currentUsername, password: ""},
      schema,
      onSubmitSuccess,
      onSubmitFailure,
      resetValuesAfterSubmission: true,
    })

    <Modal title={t("Update email")} hideFooter=true opened onRequestClose>
      <EditEmailForm.FormProvider propState=formPropState>
        {switch error {
        | Some(message) =>
          <Box spaceTop=#large spaceX=#xlarge>
            <Banner textStatus=Danger(message) onRequestClose={_ => setError(_ => None)} />
          </Box>
        | _ => React.null
        }}
        <Box spaceTop=#xlarge spaceX=#xlarge>
          <StackFields>
            <EditEmailForm.InputText field=NewUsername label={t("Email")} hideRequired=true />
            <EditEmailForm.InputPassword
              field=Password
              label={t("Current password")}
              placeholder={t("Enter your current password")}
              hideError=true
              hideRequired=true
            />
          </StackFields>
          <Box spaceY=#xlarge>
            <Inline align=#end space=#xmedium>
              <Button variation=#neutral onPress={_ => onRequestClose()}>
                {t("Cancel")->React.string}
              </Button>
              <EditEmailForm.SubmitButton
                text={t("Save")} size=#normal variation=#success onSubmit
              />
            </Inline>
          </Box>
        </Box>
      </EditEmailForm.FormProvider>
    </Modal>
  }

  let make = React.memo(make)
}

module EditEmailAndPasswordFieldset = {
  @react.component
  let make = (~username, ~onNotification, ~updateEmailRequest, ~updatePasswordRequest) => {
    let (updatePasswordModalOpened, setUpdatePasswordModalOpened) = React.useState(() => false)
    let (updateUsernameModalOpened, setUpdateUsernameModalOpened) = React.useState(() => false)

    let handleEmailClick = _ => {
      setUpdateUsernameModalOpened(_ => true)
      onNotification(None)
    }

    let handlePasswordClick = _ => {
      setUpdatePasswordModalOpened(_ => true)
      onNotification(None)
    }

    <>
      <FieldsetLayoutPanel
        title={t("Login details")}
        description={t(
          "To update your login credentials, you will need to enter your current password and confirm the changes through a link sent to your current email inbox.",
        )}>
        <Stack space=#small>
          <Stack space=#none>
            <Label text={t("Email")} />
            <View style={StyleX.props([StyleX.style(~marginTop="-8px", ())])}>
              <Inline>
                <TextStyle> {username->React.string} </TextStyle>
                <IconButton
                  marginSize=#small
                  name=#edit_light
                  onPress=handleEmailClick
                  color=Colors.neutralColor100
                  hoveredColor=Colors.brandColor60
                />
              </Inline>
            </View>
          </Stack>
          <Stack space=#none>
            <Label text={t("Password")} />
            <View style={StyleX.props([StyleX.style(~marginTop="-8px", ())])}>
              <Inline>
                <TextStyle>
                  {"● ● ● ● ● ● ● ● ● ● ● ● "->React.string}
                </TextStyle>
                <IconButton
                  marginSize=#small
                  name=#edit_light
                  onPress=handlePasswordClick
                  color=Colors.neutralColor100
                  hoveredColor=Colors.brandColor60
                />
              </Inline>
            </View>
          </Stack>
        </Stack>
      </FieldsetLayoutPanel>
      <EditEmailModal
        opened=updateUsernameModalOpened
        onRequestClose={() => setUpdateUsernameModalOpened(_ => false)}
        currentUsername=username
        onNotification
        updateEmailRequest
      />
      <EditPasswordFormModal
        opened=updatePasswordModalOpened
        onRequestClose={() => setUpdatePasswordModalOpened(_ => false)}
        onNotification
        updatePasswordRequest
      />
    </>
  }

  let make = React.memo(make)
}

module EditUserNamesFormLenses = %lenses(
  type state = {
    name: string,
    organizationName: string,
  }
)

module EditUserNamesForm = Form.Make(EditUserNamesFormLenses)

module UpdateUserNamesRequest = {
  let encodeBody = (~name, ~organizationName) =>
    Js.Dict.fromArray([
      ("name", name->Json.encodeString),
      ("organizationName", organizationName->Json.encodeString),
    ])->Json.encodeDict

  let endpoint = Env.gatewayUrl() ++ "/user"

  type makeT = (~name: string, ~organizationName: string) => Future.t<result<Json.t, Request.error>>

  let make: makeT = (~name, ~organizationName) => {
    Request.make(endpoint, ~method=#PATCH, ~bodyJson=encodeBody(~name, ~organizationName))
  }
}
let updateUserNamesRequest = UpdateUserNamesRequest.make

module EditUserNamesFieldset = {
  let schema = []

  @react.component
  let make = (~name, ~organizationName, ~onNotification, ~updateUserNamesRequest) => {
    let jwt = Auth.getJwt()->Option.getWithDefault("")
    let logUser = Auth.useLogUser()

    let onSubmitSuccess = _ => {
      logUser(jwt)
      onNotification(
        Some(Banner.Success(t("Your user and account information has been successfully updated."))),
      )
    }

    let onSubmitFailure = error => onNotification(Some(Banner.Danger(error)))

    let formPropState = EditUserNamesForm.useFormPropState({
      initialValues: {name, organizationName},
      schema,
      onSubmitSuccess,
      onSubmitFailure,
    })

    let onSubmit = (_, {EditUserNamesFormLenses.name: name, organizationName}) => {
      onNotification(None)
      updateUserNamesRequest(~name, ~organizationName)
      ->Future.mapOk(_ => Some(
        t("Your user and account information has been successfully updated."),
      ))
      ->Future.mapError(_ =>
        t("An unexpected error occured. Please try again or contact the support.")
      )
    }

    <FieldsetLayoutPanel
      title={t("Account name and Username")}
      description={t(
        "The account name is the name that appears at the top of the interface menu. The username is your name that appears at the bottom of the menu.",
      )}>
      <EditUserNamesForm.FormProvider propState=formPropState>
        <EditUserNamesForm.ControlEnterKey onSubmit />
        <Stack space=#large>
          <EditUserNamesForm.InputText field=OrganizationName label={t("Account name")} />
          <EditUserNamesForm.InputText field=Name label={t("Username")} hideError=true />
          <Inline space=#small align=#end>
            <EditUserNamesForm.SubmitButton
              onSubmit variation=#success size=#xsmall text={"  " ++ t("Save") ++ "  "}
            />
          </Inline>
        </Stack>
      </EditUserNamesForm.FormProvider>
    </FieldsetLayoutPanel>
  }

  let make = React.memo(make)
}

@react.component
let make = (
  ~updateEmailRequest: UpdateEmailRequest.makeT,
  ~updatePasswordRequest: UpdatePasswordRequest.makeT,
  ~updateUserNamesRequest: UpdateUserNamesRequest.makeT,
) => {
  let (notification, setNotification) = React.useState(_ => None)
  let auth = Auth.useState()
  let (username, name, organizationName) = switch auth {
  | Logged({user: {username, name, organizationName}}) => (username, name, organizationName)
  | _ => ("", "", "")
  }

  let handleNotification = notification => setNotification(_ => notification)

  let notificationBanner = {
    switch notification {
    | Some(notification) =>
      <Box spaceTop=#medium>
        <Banner textStatus=notification onRequestClose={_ => setNotification(_ => None)} />
      </Box>
    | None => React.null
    }
  }

  <ResourceDetailsPage title={t("Your account")} notificationBanner>
    <Stack space=#xlarge>
      <EditEmailAndPasswordFieldset
        username onNotification=handleNotification updateEmailRequest updatePasswordRequest
      />
      <EditUserNamesFieldset
        name organizationName onNotification=handleNotification updateUserNamesRequest
      />
    </Stack>
  </ResourceDetailsPage>
}
