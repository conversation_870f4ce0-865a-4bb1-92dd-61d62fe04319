// NOTE - unmount animation doesn't work because:
// - portal instantly being removed (not so clean but can be delayed)
// - Popover rendering condition may (and should) be handled by consumer

open StyleX

type t = ReactStately.Overlay.Trigger.state

type trigger = {
  state: t,
  ariaProps: ReactAria.Overlay.Trigger.t,
  ref: React.ref<Js.Nullable.t<Dom.element>>,
}

// NOTE - when Popover is consumed as standalone (ie. not inside a react aria based component)
// this hook must be called to be bound to a custom trigger element and handle the popover state.
let useTrigger = (~defaultOpened=false, ~modalPopover=false, ()) => {
  let ref = React.useRef(Js.Nullable.null)
  let (_, triggerHovered) = Hover.use(~ref, ())
  let state = ReactStately.Overlay.Trigger.useState(~props={defaultOpen: defaultOpened})
  let ariaProps = ReactAria.Overlay.Trigger.use(
    ~ref=ref->ReactDOM.Ref.domRef,
    ~props={\"type": #dialog},
    ~state,
    (),
  )
  // NOTE - prevents issue closing from the trigger would both request a close from losing focus and a toggle
  let state = {
    ...state,
    onRequestClose: () => {
      // TODO - keep investigating (aria-controls missing in returned props?)
      if !triggerHovered || modalPopover {
        state.onRequestClose()
      }
    },
  }
  {ref, state, ariaProps}
}

// NOTE - to be used within a <Popover> in combination of Popover.useTrigger
module Dialog = {
  @react.component
  let make = React.memo((
    ~children,
    ~ariaLabel="popover-dialog",
    ~ariaProps as overlayProps: ReactAria.Overlay.overlayProps,
    ~style=?,
    ~className=?,
  ) => {
    let domRef = React.useRef(Js.Nullable.null)->ReactDOM.Ref.domRef
    let {dialogProps} = ReactAria.Dialog.use(~props={\"aria-label": ariaLabel}, ~ref=domRef)

    <ReactAria.Spread props={ReactAria.mergeProps2(dialogProps, overlayProps)}>
      <div ref=domRef ?style ?className> children </div>
    </ReactAria.Spread>
  })
}

// REVIEW - to be removed: once Menu will be revamped with useMenu, the Menu items will be builtin
// allowing its items to manage the popover state such as closing it when clicking on an item.
module Context = {
  let context = React.createContext(None)

  module Provider = {
    let make = React.Context.provider(context)
  }

  exception PopoverContextNotFound
  let use = () =>
    switch React.useContext(context) {
    | Some(state) => state
    | None => raise(PopoverContextNotFound)
    }
}

// NOTE - the Popover state can be consumed within its children: eg. inside items
// being children of a component using Popover requiring a popover toggle/close action.
let useState = Context.use

module ArrowOverlay = {
  open! Svg
  open! ReactAria.Overlay.Position

  let styles = StyleX.create({
    "root": style(~position=#absolute, ~zIndex=1, ()),
  })

  @react.component
  let make = React.memo((~arrowProps, ~placement, ~crossOffset) => {
    let style = StyleX.props([
      styles["root"],
      style(
        ~transform=switch placement {
        | #top => "rotate(180deg)"
        | #right => "rotate(-90deg)"
        | #bottom => "rotate(0deg)"
        | #left => "rotate(90deg)"
        },
        (),
      ),
      switch (placement, arrowProps) {
      | (#top, {style: {left: Some(left)}}) =>
        style(~bottom="-8px", ~left=(left +. crossOffset -. 10.)->Float.toString ++ "px", ())
      | (#right, {style: {top: Some(top)}}) =>
        style(~top=(top +. crossOffset -. 10.)->Float.toString ++ "px", ~left="-8px", ())
      | (#bottom, {style: {left: Some(left)}}) =>
        style(~top="-10px", ~left=(left +. crossOffset -. 8.)->Float.toString ++ "px", ())
      | (#left, {style: {top: Some(top)}}) =>
        style(~top=(top +. crossOffset -. 10.)->Float.toString ++ "px", ~right="-8px", ())
      | _ => style()
      },
    ])

    <View style>
      <Svg width="16" height="16" viewBox="0 0 20 20">
        <Path
          d="M 9.278 6.281 Q 9.989 5.718 10.7 6.281 L 19.741 13.437 Q 20.452 14 19.029 14 L 0.949 14 Q -0.474 14 0.237 13.437 Z"
          fill=Colors.neutralColor00
          filter="drop-shadow(rgba(0, 0, 0, 0.05) 0px -1px 1px)"
          stroke=Colors.neutralColor20
          strokeDasharray="13, 21"
        />
      </Svg>
    </View>
  })
}

let styles = StyleX.create({
  "root": style(
    ~backgroundColor=Colors.neutralColor00,
    ~overflow=#hidden,
    ~boxShadow=`0px 8px 14px 0px ${Colors.neutralColor100}1A`,
    ~border="1px solid transparent",
    ~borderRadius="5px",
    (),
  ),
})

let viewStyleFromParams = (~layout, ~size, ~triggerWidth, ~borderColor) =>
  StyleX.props([
    styles["root"],
    style(~borderColor, ~borderRadius=size === #compact ? "3px" : "5px", ()),
    switch layout {
    | #auto => style()
    | #triggerMinWidth => style(~minWidth=triggerWidth->Float.toString ++ "px", ())
    | #triggerStrictWidth =>
      style(
        ~minWidth=triggerWidth->Float.toString ++ "px",
        ~maxWidth=triggerWidth->Float.toString ++ "px",
        (),
      )
    },
  ])

type layout = [#auto | #triggerMinWidth | #triggerStrictWidth]
type variation = [#normal | #arrowed]
type size = [#normal | #compact]

@react.component
let make = (
  ~children,
  ~popoverRef=?,
  ~triggerRef,
  ~state,
  ~variation=#normal,
  ~borderColor=Colors.neutralColor20,
  ~size=#normal,
  ~placement=#bottom,
  ~layout=#auto,
  ~animation=?,
  ~overlayPriority=true,
  ~dismissable=true,
  ~shouldUpdatePosition=true,
  ~modal=true,
  ~offset=3.,
  ~crossOffset=0.,
) => {
  let ref = popoverRef->Option.getWithDefault(React.useRef(Js.Nullable.null))

  // NOTE - the position is always updated until the popover is defined
  let shouldUpdatePosition =
    shouldUpdatePosition || (!shouldUpdatePosition && ref.React.current->Js.isNullable)

  // NOTE - by design, ReactAria doesn't allow to close a non modal popover with Escape key,
  // the `isKeyboardDismissDisabled` prop is used to disable the internal listener.
  React.useEffect0(() => {
    let onKeyDown = event => {
      switch WebAPI.KeyboardEvent.key(event) {
      | "Escape" => state.ReactStately.Overlay.Trigger.onRequestToggle() // NOTE - onRequestClose doesn't work as expected
      | _ => ()
      }
    }
    switch ref.current->Js.Nullable.toOption {
    | Some(element) if !modal =>
      element->WebAPI.DomElement.addKeyDownEventListener(onKeyDown)
      Some(() => element->WebAPI.DomElement.removeKeyDownEventListener(onKeyDown))
    | _ => None
    }
  })

  let props = {
    ReactAria.Popover.popoverRef: ref->ReactDOM.Ref.domRef,
    triggerRef: triggerRef->ReactDOM.Ref.domRef,
    shouldUpdatePosition,
    placement,
    offset: size === #compact ? offset -. 1. : offset,
    crossOffset,
    isNonModal: !modal,
    isKeyboardDismissDisabled: modal,
  }
  let (_, {Dimensions.width: triggerWidth}) = Dimensions.use(~ref=triggerRef, ())
  let {popoverProps, underlayProps, arrowProps, placement} = ReactAria.Popover.use(~props, ~state)

  let popoverProps = ReactAria.mergeProps3(
    popoverProps,
    {"style": ReactAria.mergeProps2(popoverProps.style, {"zIndex": !overlayPriority ? 1 : 1000})},
    {"onBlur": dismissable ? popoverProps.onBlur : None},
  )

  let animation =
    animation->Option.getWithDefault(
      triggerWidth > 550. ? #fadeTranslation : #fadeBubbleTranslation,
    )

  <Context.Provider value=Some(state)>
    <ReactAria.Overlay>
      {if modal {
        <ReactAria.Spread props=underlayProps>
          <div />
        </ReactAria.Spread>
      } else {
        React.null
      }}
      <ReactAria.Spread props=popoverProps>
        <div ref={ref->ReactDOM.Ref.domRef}>
          <AnimatedRender displayed=state.opened duration=75 animation>
            {switch variation {
            | #arrowed => <ArrowOverlay arrowProps placement crossOffset />
            | #normal => React.null
            }}
            {if modal {
              <ReactAria.DismissButton onDismiss=state.onRequestClose />
            } else {
              React.null
            }}
            <View style={viewStyleFromParams(~layout, ~size, ~triggerWidth, ~borderColor)}>
              children
            </View>
            {if modal {
              <ReactAria.DismissButton onDismiss=state.onRequestClose />
            } else {
              React.null
            }}
          </AnimatedRender>
        </div>
      </ReactAria.Spread>
    </ReactAria.Overlay>
  </Context.Provider>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.state.opened === newProps.state.opened && !newProps.state.opened
)
