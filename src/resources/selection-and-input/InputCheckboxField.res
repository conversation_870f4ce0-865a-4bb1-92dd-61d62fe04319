open StyleX

let styles = StyleX.create({
  "wrapper": style(~display=#flex, ()),
})

@react.component
let make = (
  ~label,
  ~text,
  ~required=?,
  ~tooltip=?,
  ~errorMessage=?,
  ~value,
  ~disabled=false,
  ~onChange,
) =>
  <Field label ?tooltip ?errorMessage ?required>
    <Inline space=#small>
      <View style={StyleX.props([styles["wrapper"]])}>
        <Checkbox size=18. onChange value disabled />
      </View>
      <Touchable onPress={_ => onChange(!value)} disabled>
        <TextStyle> {text->React.string} </TextStyle>
      </Touchable>
    </Inline>
  </Field>

let make = React.memo(make)
