open StyleX

let styles = StyleX.create({
  "view": style(~display=#flex, ~alignItems=#center, ~paddingBottom="0px", ()),
  "text": style(
    ~marginTop="-1px",
    ~font=`normal 400 13px "Libre Franklin"`,
    ~color=Colors.neutralColor90,
    (),
  ),
  "badge": style(~marginRight="12px", ()),
})

@react.component
let make = (
  ~label,
  ~badge=?,
  ~alignToggleButtonToStart=false,
  ~required,
  ~errorMessage=?,
  ~value,
  ~disabled=false,
  ~onChange,
) => {
  <Field ?errorMessage>
    <View
      style={StyleX.props([
        styles["view"],
        style(~justifyContent=alignToggleButtonToStart ? #"flex-start" : #"space-between", ()),
      ])}>
      <Touchable onPress={_ => onChange(!value)} disabled>
        <Box spaceRight=#normal>
          <TextStyle> {label->Field.makeLabel(~required)->React.string} </TextStyle>
        </Box>
      </Touchable>
      <Inline>
        {switch badge {
        | Some(badge) =>
          <View style={StyleX.props([styles["badge"]])}>
            <Badge variation=#information> {badge->React.string} </Badge>
          </View>
        | _ => React.null
        }}
        <ToggleSwitch onChange value disabled />
      </Inline>
    </View>
  </Field>
}

let make = React.memo(make)
