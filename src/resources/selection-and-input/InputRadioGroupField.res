// NOTE - the component works today but at some point
// it should use @react-aria/radio to improve tests/accessibility

open StyleX

let styles = StyleX.create({
  "root": style(~marginTop=Spaces.xsmallPx, ~paddingRight="20px", ()),
  "item": style(~display=#flex, ~flexDirection=#row, ~paddingBlock="0px", ()),
  "itemIcon": style(
    ~display=#flex,
    ~borderRadius="50px",
    ~backgroundColor=Colors.neutralColor00,
    ~border="1px solid transparent",
    ~justifyContent=#center,
    ~alignItems=#center,
    ~boxShadow=`0px 0.1px 1px 0px ${Colors.neutralColor100}14`,
    ~height="16px",
    ~width="16px",
    (),
  ),
  "itemIconEnabled": style(~borderColor=Colors.neutralColor50, ()),
  "itemIconDisabled": style(~borderColor=Colors.neutralColor15, ()),
  "itemText": style(
    ~marginTop="-1px",
    ~marginLeft="8px",
    ~font=`normal 400 15px "Libre Franklin"`,
    ~whiteSpace=#pre,
    (),
  ),
  "itemTextDisabled": style(~color=Colors.neutralColor50, ()),
})

let itemIconStyleFromParams = (~active, ~hovered) =>
  StyleX.props([
    styles["itemIcon"],
    switch (active, hovered) {
    | (true, _)
    | (_, true) =>
      styles["itemIconEnabled"]
    | _ => styles["itemIconDisabled"]
    },
  ])

let itemTextStyleFromParams = (~disabled, ~active) =>
  StyleX.props2([
    styles["itemText"],
    switch (disabled, active) {
    | (true, false) => styles["itemTextDisabled"]
    | _ => style()
    },
  ])

module Item = {
  @react.component
  let make = React.memo((~children, ~active, ~onPress, ~disabled) => {
    let (ref, hovered) = Hover.use()

    <Touchable disabled onPress={_ => onPress()}>
      <View ref style={StyleX.props([styles["item"]])}>
        <View style={itemIconStyleFromParams(~active, ~hovered={!disabled && hovered})}>
          <Icon
            size=16. fill={active ? Colors.brandColor60 : Colors.neutralColor00} name=#inner_oval
          />
        </View>
        <span {...itemTextStyleFromParams(~disabled, ~active)}> children </span>
      </View>
    </Touchable>
  })
}

@react.component
let make = (
  ~label=?,
  ~value,
  ~required,
  ~onChange,
  ~isEqualValue=(a, b) => a === b,
  ~errorMessage=?,
  ~options,
  ~optionToText,
  ~optionToDisable=?,
) => {
  <Field ?label required ?errorMessage>
    <View style={StyleX.props([styles["root"]])}>
      <Stack space=#normal>
        {options
        ->Array.map(item =>
          <Item
            active={isEqualValue(value, item)}
            onPress={() => onChange(item)}
            disabled={switch optionToDisable {
            | Some(optionToDisable) => optionToDisable(item)
            | None => false
            }}>
            {item->optionToText->React.string}
          </Item>
        )
        ->React.array}
      </Stack>
    </View>
  </Field>
}
