// TODO - Externalize some parts of this component to primitives

let containerHeight = 40.
let buttonHeight = containerHeight /. 2.
let borderRadius = 5.

let px = value => value->Float.toString ++ "px"
let pct = value => value->Float.toString ++ "%"

let borderColor = (~hovered, ~focused, ~errored) =>
  if focused && errored {
    Colors.dangerColor70
  } else if !focused && errored {
    Colors.dangerColor50
  } else if focused && !errored {
    Colors.neutralColor30
  } else if hovered && !focused && !errored {
    Colors.neutralColor25
  } else {
    Colors.neutralColor20
  }

let containerStyle = (~shrinkInput) =>
  StyleX.style(
    ~display=#"inline-grid",
    ~gridTemplateAreas=`
      "input appender increment"
      "input appender decrement"
    `,
    ~gridTemplateColumns=`${shrinkInput ? "0" : "1"}fr 0fr auto`,
    (),
  )

let inputStyle = (~strong, ~hovered, ~focused, ~disabled, ~stepper, ~errored, ~placeholder) =>
  ReactDOM.Style.make(
    ~flex="1",
    ~gridArea="input",
    ~minWidth=60.->px,
    ~width=100.->pct,
    ~fontWeight=strong ? "600" : "initial", // semi-bold
    ~height=containerHeight->px,
    ~backgroundColor=disabled ? Colors.neutralColor05 : Colors.neutralColor00,
    ~paddingLeft=Spaces.normal->px,
    ~borderStyle="solid",
    ~borderWidth=1.->px,
    ~boxSizing="border-box",
    ~borderRadius=stepper ? 0.->px : borderRadius->px,
    ~borderTopLeftRadius=borderRadius->px,
    ~borderBottomLeftRadius=borderRadius->px,
    ~color=if !placeholder {
      Colors.neutralColor90
    } else {
      Colors.placeholderTextColor
    },
    ~borderColor=borderColor(~hovered=hovered && !disabled, ~focused, ~errored),
    ~fontSize=15.->px,
    ~fontFamily=#LibreFranklin->FontFaces.fontFamilyFromFontName,
    (),
  )

let appenderStyle = (~hovered, ~focused, ~disabled, ~stepper, ~errored) =>
  StyleX.style(
    ~gridArea="appender",
    ~display=#flex,
    ~minWidth="max-content",
    ~justifyContent=#center,
    ~alignItems=#center,
    ~marginLeft=-3.->px,
    ~paddingRight=(Spaces.normal -. 4.)->px,
    ~paddingLeft=5.->px,
    ~paddingTop=1.->px,
    ~background=disabled ? Colors.neutralColor05 : Colors.neutralColor00,
    ~borderTop="1px solid " ++ borderColor(~hovered=hovered && !disabled, ~focused, ~errored),
    ~borderRight="1px solid " ++ borderColor(~hovered=hovered && !disabled, ~focused, ~errored),
    ~borderBottom="1px solid " ++ borderColor(~hovered=hovered && !disabled, ~focused, ~errored),
    ~borderLeft="0",
    ~borderTopRightRadius=stepper ? 0.->px : borderRadius->px,
    ~borderBottomRightRadius=stepper ? 0.->px : borderRadius->px,
    (),
  )

let buttonStyle = (~hovered, ~focused, ~errored, ~pressed, ~disabled, ~mode) =>
  ReactDOM.Style.combine(
    ReactDOM.Style.make(
      ~display="flex",
      ~width=18.->px,
      ~justifyContent="center",
      ~alignItems="center",
      ~marginLeft=-1.->px,
      ~cursor=disabled ? "default" : "pointer",
      ~backgroundColor=switch (pressed, disabled) {
      | (_, true)
      | (true, _) => Colors.neutralColor10
      | _ => Colors.neutralColor00
      },
      ~boxSizing="border-box",
      ~borderStyle="solid",
      ~borderWidth=1.->px,
      ~borderColor=borderColor(~hovered=hovered && !disabled, ~focused, ~errored),
      (),
    ),
    switch mode {
    | #increment =>
      ReactDOM.Style.make(
        ~gridArea="increment",
        ~height=buttonHeight->px,
        ~borderTopRightRadius=borderRadius->px,
        ~borderBottomWidth="0",
        (),
      )
    | #decrement =>
      ReactDOM.Style.make(
        ~gridArea="decrement",
        ~height=buttonHeight->px,
        ~borderBottomRightRadius=borderRadius->px,
        (),
      )
    },
  )

type appender =
  | Currency(Intl.currency)
  | Percent
  | Custom(string)

let appenderCurrency = currency => Currency(currency)
let appenderPercent = Percent
let appenderCustom = custom => Custom(custom)

module Button = {
  type t = [#increment | #decrement]

  @react.component
  let make = React.memo((~props, ~mode: t, ~hovered, ~focused, ~errored, ~disabled, ~onPress=?) => {
    let derivedHovered = hovered

    let (ref, hovered) = Hover.use()
    let (pressed, setPressed) = React.useState(() => false)
    let {buttonProps: props} = ReactAria.Button.use(
      ~props=ReactAria.mergeProps2(
        props,
        {
          ReactAria.Button.elementType: #div,
          disabled,
          onPressStart: _ => setPressed(_ => true),
          onPressEnd: _ => {
            setPressed(_ => false)
            onPress->Option.forEach(x => x())
          },
        },
      ),
      ~ref=ref->ReactDOM.Ref.domRef,
      (),
    )

    <ReactAria.Spread props>
      <div
        ref={ref->ReactDOM.Ref.domRef}
        style={buttonStyle(
          ~hovered=derivedHovered || hovered,
          ~focused,
          ~errored,
          ~disabled,
          ~pressed,
          ~mode,
        )}>
        <Icon
          name={switch mode {
          | #increment => #arrow_up_light
          | #decrement => #arrow_down_light
          }}
          fill={switch (hovered, disabled) {
          | (true, false) => Colors.neutralColor100
          | (_, true) => Colors.neutralColor25
          | _ => Colors.neutralColor50
          }}
          size=12.
        />
      </div>
    </ReactAria.Spread>
  })
}

module OptionalValue = {
  @react.component
  let make = (
    ~value,
    ~minValue=?,
    ~maxValue=?,
    ~step=?,
    ~minPrecision=?,
    ~precision=2,
    ~label=?,
    ~tooltip=?,
    ~errorMessage=?,
    ~placeholder=?,
    ~appender=?,
    ~shrinkInput=false,
    ~autoFocused=false,
    ~focused=false,
    ~required=false,
    ~disabled=false,
    ~strongStyle=false,
    ~hideStepper=false,
    ~useGrouping=true,
    ~allowsSideEffectChange=false, // NOTE - use with caution
    ~onChange,
    ~onFocus=?,
    ~onBlur=?,
    ~testLocalization=?,
  ) => {
    let derivedFocused = focused
    let derivedOnChange = onChange

    let (focused, setFocused) = React.useState(() => autoFocused || derivedFocused)
    let inputRef = React.useRef(Js.Nullable.null)
    let inputDomRef = inputRef->ReactDOM.Ref.domRef
    let (_, hovered) = Hover.use(~ref=inputRef, ())

    let precision = switch minPrecision {
    | Some(minPrecision) => minPrecision > precision ? minPrecision : precision
    | None => precision
    }

    ReactUpdateEffect.use1(() => {
      setFocused(_ => derivedFocused)
      None
    }, [derivedFocused])

    // Sets the formatting options and conditions for precision
    let formatOptions = React.useMemo2(() =>
      switch (focused, precision) {
      | (false, _) | (_, 0) => {
          ReactStately.NumberField.minimumFractionDigits: minPrecision->Option.getWithDefault(
            precision,
          ),
          maximumFractionDigits: precision,
          useGrouping,
        }
      | _ => {
          useGrouping,
          maximumFractionDigits: precision,
        }
      }
    , (focused, precision))

    // Sends new controlled value upon input and steppers pressing
    let onChange = React.useCallback4(nextValue => {
      let handler = () =>
        if value !== Some(nextValue) {
          derivedOnChange(nextValue->Js.Float.isFinite ? Some(nextValue) : None)
        }

      switch (minValue, maxValue) {
      | (Some(min), Some(max)) if nextValue >= min && nextValue <= max => handler()
      | (None, Some(max)) if nextValue <= max => handler()
      | (Some(min), None) if nextValue >= min => handler()
      | (None, None) => handler()
      | _ => ()
      }
    }, (derivedOnChange, minValue, maxValue, value))

    // Callbacks input interactions passed to NumberField hook
    let onFocusChange = React.useCallback0(value => setFocused(_ => value))
    let onKeyDown = React.useCallback2(event =>
      switch (focused, WebAPI.KeyboardEvent.key(event)) {
      | (true, "Enter") =>
        inputRef
        ->ReactDomElement.fromRef
        ->Option.map(domElement => domElement->WebAPI.DomElement.blur)
        ->ignore
      | (true, "Escape") => event->WebAPI.KeyboardEvent.continuePropagation
      | _ => ()
      }
    , (focused, value))

    let props = {
      ReactStately.NumberField.locale: testLocalization->Option.getWithDefault(Intl.locale),
      \"aria-label": label->Option.getWithDefault(Intl.t("Numeric input field")),
      ?placeholder,
      isDisabled: disabled,
      autoFocus: autoFocused,
      ?step,
      formatOptions,
      ?minValue,
      ?maxValue,
      defaultValue: ?(focused ? value->Option.orElse(Some(Js.Float._NaN)) : None),
      value: ?(!focused ? value->Option.orElse(Some(Js.Float._NaN)) : None),
      onChange,
      onKeyDown,
      ?onFocus,
      onFocusChange,
      ?onBlur,
    }

    let state = ReactStately.NumberField.useState(~props)
    let props = ReactAria.NumberField.use(~props, ~state, ~ref=inputDomRef, ())

    // Updates the input value because of unsync defaultValue used on focus
    ReactUpdateEffect.useLayout1(() => {
      if focused {
        let inputValue = switch value {
        | Some(value) if Js.Float.isFinite(value) =>
          value->Intl.decimalFormat(
            ~locale=testLocalization->Option.getWithDefault(Intl.locale),
            ~maximumFractionDigits=?formatOptions.maximumFractionDigits,
            ~minimumFractionDigits=?formatOptions.minimumFractionDigits,
            ~useGrouping,
          )
        | _ => ""
        }
        state.setInputValue(inputValue)
      }
      None
    }, [focused])

    // Sends value during the input instead of on blur
    ReactUpdateEffect.use1(() => {
      let allowsSideEffectChange = allowsSideEffectChange || focused
      if (
        allowsSideEffectChange &&
        Some(state.numberValue) !== value &&
        !(state.numberValue->Js.Float.isNaN)
      ) {
        onChange(state.numberValue)
      }
      None
    }, [state.numberValue])

    // Sends (control) value when current input is NaN (empty field)
    ReactUpdateEffect.useLayout1(() => {
      if !(state.numberValue->Js.Float.isFinite) {
        derivedOnChange(None)
      }
      None
    }, [state.numberValue])

    // NOTE - workaround for placeholder hook props issue
    let inputProps = React.useMemo2(() =>
      ReactAria.mergeProps2(
        props.inputProps,
        {
          "placeholder": placeholder->Option.getWithDefault(""),
        },
      )
    , (props.inputProps, placeholder))

    // Main input style && computes input width to fit its text content
    let inputStyle = {
      let inputStyle = inputStyle(
        ~strong=strongStyle,
        ~hovered,
        ~focused,
        ~disabled,
        ~stepper=!hideStepper,
        ~errored=errorMessage->Option.isSome,
        ~placeholder=switch (value, focused) {
        | (Some(0.), false) => true
        | (Some(value), _) => value->Js.Float.isNaN
        | _ => false
        },
      )
      let value = state.inputValue
      let valueDecimal = value->Js.String2.includes(".") || value->Js.String2.includes(",")
      let valueLength = value->Js.String2.length->Int.toFloat -. (valueDecimal ? 1. : 0.)

      if shrinkInput {
        ReactDOM.Style.combine(
          inputStyle,
          ReactDOM.Style.make(~minWidth=`calc(25px + ${valueLength->Float.toString}ch)`, ()),
        )
      } else {
        inputStyle
      }
    }

    let appenderStyle = appenderStyle(
      ~hovered,
      ~focused,
      ~disabled,
      ~stepper=!hideStepper,
      ~errored=errorMessage->Option.isSome,
    )

    <Field ?label ?tooltip required ?errorMessage>
      <View style={StyleX.props([containerStyle(~shrinkInput)])}>
        <ReactAria.Spread props={inputProps}>
          <input ref={inputDomRef} style={inputStyle} />
        </ReactAria.Spread>
        {switch appender {
        | Some(appender) =>
          <View style={StyleX.props([appenderStyle])}>
            <TextStyle size=#large opacity=0.3>
              {switch appender {
              | Currency(currency) => currency->Intl.toCurrencySymbol
              | Percent => "%"
              | Custom(value) => value
              }->React.string}
            </TextStyle>
          </View>
        | None => React.null
        }}
        {if !hideStepper {
          <>
            <Button
              mode=#increment
              props=props.incrementButtonProps
              hovered
              disabled={!state.canIncrement}
              errored={errorMessage->Option.isSome}
              focused
              onPress={() =>
                // NOTE - small hack to fix incrementing from an empty value
                if state.numberValue->Js.Float.isNaN || state.numberValue === 0. {
                  state.increment()
                }}
            />
            <Button
              mode=#decrement
              props=props.decrementButtonProps
              hovered
              disabled={!state.canDecrement}
              errored={errorMessage->Option.isSome}
              focused
            />
          </>
        } else {
          React.null
        }}
      </View>
    </Field>
  }

  let make = React.memo(make)
}

@react.component
let make = (
  ~value,
  ~minValue=?,
  ~maxValue=?,
  ~step=?,
  ~minPrecision=?,
  ~precision: int=2,
  ~label=?,
  ~tooltip=?,
  ~errorMessage=?,
  ~placeholder=?,
  ~appender=?,
  ~shrinkInput=false,
  ~autoFocused=false,
  ~focused=false,
  ~required=false,
  ~disabled=false,
  ~strongStyle=false,
  ~hideStepper=false,
  ~useGrouping=true,
  ~allowsSideEffectChange=false, // NOTE - use with caution
  ~onChange,
  ~onFocus=?,
  ~onBlur=?,
  ~testLocalization=?,
) => {
  let onChange = React.useCallback2(nextValue =>
    switch nextValue {
    | Some(nextValue) => onChange(nextValue)
    | None => onChange(value)
    }
  , (onChange, value))

  <OptionalValue
    ?label
    ?tooltip
    ?placeholder
    ?appender
    ?errorMessage
    ?minValue
    ?maxValue
    ?step
    ?minPrecision
    hideStepper
    disabled
    focused
    required
    useGrouping
    precision
    strongStyle
    shrinkInput
    autoFocused
    allowsSideEffectChange
    value=Some(value)
    onChange
    ?onFocus
    ?onBlur
    ?testLocalization
  />
}

let make = React.memo(make)
