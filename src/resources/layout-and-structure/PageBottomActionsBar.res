open WebAPI
open StyleX

type navOpened = option<bool>

let styles = StyleX.create({
  "wrapper": style(
    ~position=#fixed,
    ~zIndex=10,
    ~bottom="0px",
    ~left="0px",
    ~right="0px",
    ~height="60px",
    ~backgroundColor=Colors.transparent,
    ~transition="all 0.3s cubic-bezier(0.190, 1.000, 0.220, 1.000)",
    (),
  ),
  "wrapperHidden": style(
    ~height="0px",
    ~overflow=#hidden,
    ~transition="all 0.35s cubic-bezier(0.190, 1.000, 0.220, 1.000)",
    (),
  ),
  "inner": style(
    ~zIndex=10,
    ~height="100%",
    ~display=#flex,
    ~alignItems=#center,
    ~justifyContent=#"space-between",
    ~paddingInline="15px",
    ~paddingBlock="20px",
    ~backgroundColor=Colors.neutralColor00,
    ~boxShadow=`0px 0.3px 10px 0px ${Colors.neutralColor50}33`,
    (),
  ),
})

let wrapperStyleFromParams = (~navOpened: navOpened, ~show) =>
  StyleX.arrayStyle([
    !show ? styles["wrapperHidden"] : style(),
    switch navOpened {
    | None => style(~left="0px", ())
    | Some(false) => style(~left=(Nav.closedSize +. 1.)->Float.toString ++ "px", ())
    | Some(true) => style(~left=(Nav.openedSize +. 1.)->Float.toString ++ "px", ())
    },
  ])

@react.component
let make = (~displayThreshold=100., ~renderStart=() => React.null, ~renderEnd, ~navOpened=?) => {
  let (show, setShow) = React.useState(_ => false)

  let onScroll = React.useCallback0(_ => {
    let scrolled = window->Window.pageYOffset
    if scrolled >= displayThreshold && !show {
      setShow(_ => true)
    } else if scrolled < displayThreshold {
      setShow(_ => false)
    }
  })

  React.useEffect0(_ => {
    window->Window.addEventListener("scroll", onScroll)
    Some(_ => window->Window.removeEventListener("scroll", onScroll))
  })

  <View style={StyleX.props([styles["wrapper"], wrapperStyleFromParams(~navOpened, ~show)])}>
    <View style={StyleX.props([styles["inner"]])}>
      <Box spaceX=#medium>
        <Inline> {renderStart()} </Inline>
      </Box>
      <Box spaceX=#medium> {renderEnd()} </Box>
    </View>
  </View>
}

let make = React.memo(make)
