open StyleX

let styles = StyleX.create({
  "icon": style(~display=#flex, ~paddingInline="3px", ()),
  "contentView": style(
    ~overflow=#hidden,
    ~transition="all .5s cubic-bezier(0.190, 1.000, 0.220, 1.000) 0s",
    (),
  ),
})

let contentViewStyleFromParams = (~opened) =>
  StyleX.props([
    styles["contentView"],
    opened
      ? style(~opacity=1., ~paddingTop=Spaces.xsmallPx, ())
      : style(~opacity=0., ~height="0px", ~paddingBlock="0px", ()),
  ])

@react.component
let make = (~children, ~triggerShowView, ~triggerHideView, ~onToggle=?) => {
  let (opened, setOpened) = React.useState(() => false)
  let (ref, hovered) = Hover.use()

  let onPress = _ => {
    setOpened(_ => !opened)
    switch onToggle {
    | Some(onToggle) => onToggle(!opened)
    | _ => ()
    }
  }

  <View>
    <Inline>
      <Touchable ref onPress>
        {if opened {
          <Inline>
            <View style={StyleX.props([styles["icon"]])}>
              <Icon name=#arrow_up_light />
            </View>
            triggerHideView
          </Inline>
        } else {
          <View
            style={StyleX.props([
              style(
                ~borderBottom="1px solid " ++ (
                  hovered ? Colors.neutralColor20 : Colors.transparent
                ),
                (),
              ),
            ])}>
            <Inline>
              <View style={StyleX.props([styles["icon"]])}>
                <Icon name=#arrow_down_light />
              </View>
              triggerShowView
            </Inline>
          </View>
        }}
      </Touchable>
    </Inline>
    <View style={contentViewStyleFromParams(~opened)}> children </View>
  </View>
}

let make = React.memo(make)
