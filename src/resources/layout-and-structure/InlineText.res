open StyleX

let styles = StyleX.create({
  "root": style(~display=#inline, ()),
  "textAlignLeft": style(~textAlign=#left, ()),
  "textAlignJustify": style(~textAlign=#justify, ()),
  "textAlignCenter": style(~textAlign=#center, ()),
  "textAlignRight": style(~textAlign=#right, ()),
})

let textStyleFromParams = (~linespace, ~align, ~wrap, ~maxLines) =>
  StyleX.props2([
    styles["root"],
    style(
      ~whiteSpace=wrap ? #normal : #nowrap,
      ~lineHeight=wrap ? "auto" : `calc(100% + ${linespace->Spaces.toFloat->Float.toString}px)`,
      (),
    ),
    switch maxLines {
    | Some(qty) =>
      style(
        ~overflow=#hidden,
        ~display=#"-webkit-box",
        ~\"WebkitBoxOrient"=#vertical,
        ~\"WebkitLineClamp"=Int.toString(qty),
        (),
      )
    | None => style()
    },
    switch align {
    | #left => styles["textAlignLeft"]
    | #justify => styles["textAlignJustify"]
    | #center => styles["textAlignCenter"]
    | #right => styles["textAlignRight"]
    },
  ])

type alignment = [#left | #justify | #center | #right]

@react.component
let make = (~children, ~align=#left, ~wrap=true, ~linespace=#xsmall, ~maxLines=?) =>
  <span {...textStyleFromParams(~linespace, ~align, ~wrap, ~maxLines)}> children </span>
