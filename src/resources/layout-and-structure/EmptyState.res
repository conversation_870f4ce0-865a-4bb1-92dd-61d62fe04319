open StyleX
open Intl

let styles = StyleX.create({
  "container": style(
    ~flex="1",
    ~display=#flex,
    ~flexDirection=#column,
    ~alignSelf=#center,
    ~justifyContent=#center,
    ~alignItems=#center,
    ~paddingTop="8%",
    ~paddingBottom="10%",
    ~marginTop="-1.5%",
    (),
  ),
})

@react.component
let make = React.memo((~illustration, ~title, ~text=?, ~children=?) =>
  <View style={StyleX.props([styles["container"]])}>
    <Box spaceY=#small>
      <Illustration element={illustration} />
    </Box>
    <Stack>
      <Box spaceY=#xsmall>
        <Title level=#3 align=#center> {title->React.string} </Title>
      </Box>
      {switch text {
      | Some(text) =>
        <Box spaceX=#xhuge>
          <TextStyle variation=#normal align=#center> {text->React.string} </TextStyle>
        </Box>
      | None => React.null
      }}
      {switch children {
      | Some(children) => <Box spaceX=#xhuge spaceY=#large> {children} </Box>
      | _ => React.null
      }}
    </Stack>
  </View>
)

module Component = {
  let make = make
}

let error =
  <Component
    illustration=Illustration.error
    title={t("Loading issue.")}
    text={t("Please try refreshing the page.")}
  />

let loading =
  <View style={StyleX.props([styles["container"]])}>
    <Box spaceY=#small>
      <Spinner size=38. />
    </Box>
    <Stack>
      <Box spaceY=#xsmall>
        <Title level=#3 align=#center> {t("Loading...")->React.string} </Title>
      </Box>
      <Box spaceX=#xhuge>
        <TextStyle variation=#normal align=#center> {t("Please wait.")->React.string} </TextStyle>
      </Box>
    </Stack>
  </View>
