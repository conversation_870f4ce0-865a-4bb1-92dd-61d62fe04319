open StyleX

let styles = StyleX.create({
  "main": style(~borderBottom="1px solid " ++ Colors.neutralColor15, ()),
})

// TODO - https://react-spectrum.adobe.com/react-aria/useSeparator.html
// TODO - merge the Separator.res and Divider.res as they share identical logic/purpose
@react.component
let make = (~spaceY=?, ~spaceTop=?, ~spaceBottom=?) =>
  <Box ?spaceY ?spaceTop ?spaceBottom>
    <View style={StyleX.props([styles["main"]])}> React.null </View>
  </Box>

let make = React.memo(make)
