open StyleX

type variation = [#common | #table | #unset]
let variationCommon = #common

module Action = {
  type handler =
    | Callback(unit => unit)
    | OpenLink(Navigation.to)
    | OpenLinkNewTab(Navigation.to)

  type t = {
    icon: Icon.t,
    title: string,
    handler: handler,
  }
}

let actionOpenLinkNewTab = (~icon, ~title, ~to) => {
  Action.icon,
  title,
  handler: OpenLinkNewTab(to),
}

let actionCallback = (~icon, ~title, ~callback) => {
  Action.icon,
  title,
  handler: Callback(callback),
}

let styles = StyleX.create({
  "container": style(
    ~display=#flex,
    ~flexDirection=#column,
    ~paddingBlock=Spaces.normalPx,
    ~borderRadius="5px",
    ~backgroundColor=Colors.neutralColor00,
    ~minWidth="0px",
    (),
  ),
  "grow": style(~flex="1", ()),
  "shadow": style(~boxShadow=`3px 0px 10px 0px ${Colors.neutralColor50}1A`, ()),
  "title": style(~display=#flex, ~justifyContent=#"space-between", ()),
  "centerContent": style(~justifyContent=#center, ()),
})

let styleProps = (~shadowed, ~grow, ~centerContent) =>
  StyleX.props([
    styles["container"],
    shadowed ? styles["shadow"] : style(),
    grow ? styles["grow"] : style(),
    centerContent ? styles["centerContent"] : style(),
  ])

@react.component
let make = (
  ~children,
  ~title=?,
  ~action: option<Action.t>=?,
  ~variation=#common,
  ~shadowed=false,
  ~grow=false,
  ~centerContent=false,
) => {
  let actionButton = switch action {
  | Some({icon, title, handler}) =>
    <Tooltip content={<Tooltip.Span text=title />} placement=#top delay=250 closeDelay=0>
      {switch handler {
      | Callback(onPress) =>
        <IconButton
          name=icon
          onPress={_ => onPress()}
          color=Colors.neutralColor100
          hoveredColor=Colors.brandColor60
        />
      | OpenLink(to) =>
        <IconLink to name=icon color=Colors.neutralColor100 hoveredColor=Colors.brandColor60 />
      | OpenLinkNewTab(to) =>
        <IconLink
          to name=icon openNewTab=true color=Colors.neutralColor100 hoveredColor=Colors.brandColor60
        />
      }}
    </Tooltip>
  | _ => React.null
  }

  <View style={styleProps(~shadowed, ~grow, ~centerContent)}>
    {switch variation {
    | #table =>
      <Box spaceY=#medium spaceTop=#small spaceBottom=#medium>
        {switch title {
        | Some(title) =>
          <Box spaceX=#large spaceBottom=#xnormal>
            <View style={StyleX.props([styles["title"]])}>
              <Title level=#3 weight=#strong> {title->React.string} </Title>
              {actionButton}
            </View>
          </Box>
        | None => React.null
        }}
        <Stack space=#none> children </Stack>
      </Box>
    | #common =>
      <Box spaceX=#large spaceTop=#small spaceBottom=#medium>
        {switch title {
        | Some(title) =>
          <Box spaceBottom=#large>
            <View style={StyleX.props([styles["title"]])}>
              <Title level=#3 weight=#strong> {title->React.string} </Title>
              {actionButton}
            </View>
          </Box>
        | None => React.null
        }}
        <Stack space=#xlarge> children </Stack>
      </Box>
    | #unset =>
      <>
        {switch title {
        | Some(title) =>
          <Box spaceBottom=#medium>
            <View style={StyleX.props([styles["title"]])}>
              <Title level=#3 weight=#strong> {title->React.string} </Title>
              {actionButton}
            </View>
          </Box>
        | None => React.null
        }}
        <Stack space=#xlarge> children </Stack>
      </>
    }}
  </View>
}

let make = React.memo(make)

React.setDisplayName(make, "Card")
