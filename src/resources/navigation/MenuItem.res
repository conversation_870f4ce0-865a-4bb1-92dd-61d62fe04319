open StyleX

let styles = StyleX.create({
  "wrapper": style(~flex="1", ()),
  "view": style(
    ~display=#flex,
    ~flexDirection=#row,
    ~justifyContent=#"flex-start",
    ~alignItems=#center,
    ~backgroundColor=Colors.neutralColor00,
    (),
  ),
  "hoveredView": style(~backgroundColor=Colors.neutralColor05, ()),
  "iconView": style(~marginRight="10px", ()),
  "xxsmallView": style(~paddingInline="3px", ~height="28px", ()),
  "xsmallView": style(~paddingInline="10px", ~height="30px", ()),
  "smallView": style(~paddingInline="12px", ~height="36px", ()),
  "normalView": style(~paddingInline="16px", ~height="40px", ()),
  "mediumView": style(~paddingInline="30px", ~height="44px", ()),
  "largeView": style(~paddingInline="36px", ~height="60px", ()),
  "xlargeView": style(~paddingInline="38px", ~height="60px", ()),
  "text": style(~font=`normal 500 15px "Archivo"`, ()),
  "lightText": style(~color=Colors.neutralColor20, ()),
  "grayText": style(~color=Colors.neutralColor90, ()),
  "darkText": style(~color=Colors.neutralColor100, ()),
  "dangerText": style(~color=Colors.dangerColor50, ()),
})

let viewStyleFromParams = (~size, ~hovered) =>
  [
    switch size {
    | #xxsmall => styles["xxsmallView"]
    | #xsmall => styles["xsmallView"]
    | #small => styles["smallView"]
    | #large => styles["largeView"]
    | #normal
    | _ =>
      styles["normalView"]
    },
    hovered ? styles["hoveredView"] : style(),
  ]->StyleX.arrayStyle

let textStyleFromParams = (~size, ~disabled, ~variation) =>
  [
    style(~fontSize=size->FontSizes.toPx, ()),
    switch (disabled, variation) {
    | (true, _) => styles["lightText"]
    | (false, #danger) => styles["dangerText"]
    | _ => styles["darkText"]
    },
  ]->StyleX.arrayStyle

type action =
  | Callback(unit => unit)
  | OpenLink(Navigation.to)

type content = Text(string) | Component(React.element)
type textVariation = [#normal | #danger]

@react.component
let make = (
  ~content,
  ~textVariation=#normal,
  ~size=#normal,
  ~disabled=false,
  ~shouldCloseOnPress=true,
  ~action,
) => {
  let state = Popover.useState()
  let (ref, hovered) = Hover.use()

  let component =
    <View style={StyleX.props([styles["view"], viewStyleFromParams(~size, ~hovered)])}>
      {switch content {
      | Text(text) =>
        <span
          {...StyleX.props2([
            styles["text"],
            textStyleFromParams(~size, ~disabled, ~variation=textVariation),
          ])}>
          {text->React.string}
        </span>
      | Component(component) => component
      }}
    </View>

  <View ref style={StyleX.props([styles["wrapper"]])}>
    {switch action {
    | Callback(action) =>
      let onPress = _ => {
        action()
        if shouldCloseOnPress {
          state.onRequestClose()
        }
      }

      <Touchable disabled onPress> component </Touchable>
    | OpenLink(to) =>
      let onPress = _ =>
        if shouldCloseOnPress {
          state.onRequestClose()
        }

      <Navigation.Link to disabled onPress> component </Navigation.Link>
    }}
  </View>
}

let make = React.memo(make)
