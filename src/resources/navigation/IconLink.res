open StyleX

let styles = StyleX.create({
  "view": style(
    ~display=#flex,
    ~flexDirection=#column,
    ~justifyContent=#center,
    ~alignItems=#center,
    ~borderRadius="50px",
    (),
  ),
  "xxsmallView": style(~width="20px", ~height="26px", ()),
  "xsmallView": style(~width="26px", ~height="26px", ()),
  "smallView": style(~width="35px", ~height="35px", ()),
  "normalView": style(~width="40px", ~height="40px", ()),
})

let viewStyleFromParams = (~marginSize as size) =>
  switch size {
  | Some(#xxsmall) => styles["xxsmallView"]
  | Some(#xsmall) => styles["xsmallView"]
  | Some(#small) => styles["smallView"]
  | Some(#normal) => styles["normalView"]
  | _ => style()
  }

let toRoute = route => Navigation.Route(route)

@react.component
let make = React.forwardRef((
  ~to,
  ~openNewTab=false,
  ~name,
  ~marginSize=?,
  ~color=Colors.neutralColor50,
  ~hoveredColor=?,
  ~disabled=false,
  ref,
) => {
  let (ref, hovered) = Hover.use(~ref=?ref->Js.Nullable.toOption, ())

  <Navigation.Link to openNewTab disabled>
    <View ref style={StyleX.props([styles["view"], viewStyleFromParams(~marginSize)])}>
      <Icon
        name
        fill={switch (hovered, disabled, hoveredColor) {
        | (true, false, Some(active)) => active
        | _ => color
        }}
      />
    </View>
  </Navigation.Link>
})

let make = React.memo(make)
