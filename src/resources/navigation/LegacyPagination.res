open Intl
open StyleX

type action =
  | First
  | Prev
  | Next
  | Last

let isButtonDisabled = (~action, ~currentPage, ~totalPages) =>
  switch action {
  | First | Prev => currentPage <= 1
  | Next | Last => currentPage >= totalPages
  }

module ControlButton = {
  let styles = StyleX.create({
    "view": style(~margin="-7px", ()),
  })

  @react.component
  let make = (~currentPage, ~totalPages, ~action, ~onPress) => {
    let disabled = isButtonDisabled(~action, ~currentPage, ~totalPages)

    <View style={StyleX.props([styles["view"]])}>
      <IconButton
        name={switch action {
        | First => #double_arrow_left_light
        | Prev => #arrow_left_light
        | Next => #arrow_right_light
        | Last => #double_arrow_right_light
        }}
        marginSize=#small
        disabled
        onPress={_ => onPress(action)}
        color={disabled ? Colors.neutralColor20 : Colors.neutralColor90}
      />
    </View>
  }

  let make = React.memo(make)
}

module PageButton = {
  let styles = StyleX.create({
    "view": style(
      ~display=#flex,
      ~flexDirection=#column,
      ~justifyContent=#center,
      ~height="28px",
      ~minWidth="28px",
      ~paddingInline="5px",
      ~marginHorizontal="1px",
      (),
    ),
    "disabledView": style(~backgroundColor=Colors.neutralColor10, ()),
    "text": style(
      ~font=`normal 400 13px "Libre Franklin"`,
      ~color=Colors.neutralColor30,
      ~textAlign=#center,
      (),
    ),
    "disabledText": style(
      ~font=`normal 700 13px "Libre Franklin"`,
      ~color=Colors.neutralColor90,
      (),
    ),
  })

  let viewStyleFromParams = (~disabled) =>
    StyleX.props([styles["view"], disabled ? styles["disabledView"] : style()])
  let textStyleFromParams = (~disabled) =>
    StyleX.props2([styles["text"], disabled ? styles["disabledText"] : style()])

  @react.component
  let make = (~currentPage, ~totalPages, ~action=?, ~children, ~onPress) => {
    let disabled = switch action {
    | Some(action) => isButtonDisabled(~action, ~currentPage, ~totalPages)
    | None => true
    }

    let onPress = _ => {
      switch action {
      | Some(action) => onPress(action)
      | None => ()
      }
    }

    <Touchable disabled onPress={_ => onPress(action)}>
      <View style={viewStyleFromParams(~disabled)}>
        <span {...textStyleFromParams(~disabled)}> {children} </span>
      </View>
    </Touchable>
  }

  let make = React.memo(make)
}

let styles = StyleX.create({
  "container": style(
    ~display=#flex,
    ~height="64px",
    ~flexDirection=#row,
    ~alignItems=#center,
    ~justifyContent=#"space-between",
    ~backgroundColor=Colors.neutralColor00,
    ~paddingInline=Spaces.largePx,
    ~borderTop="1px solid " ++ Colors.neutralColor15,
    (),
  ),
  "label": style(~font=FontFaces.libreFranklinRegular, ~color=Colors.neutralColor30, ()),
})

@react.component
let make = (~currentPage, ~totalPages, ~onRequestPaginate as onPress) =>
  <View style={StyleX.props([styles["container"]])}>
    <span {...StyleX.props2([styles["label"]])}>
      {("Page " ++ (Int.toString(currentPage) ++ (t(" of ") ++ Int.toString(totalPages))))
        ->React.string}
    </span>
    <Inline>
      <ControlButton action=First onPress currentPage totalPages />
      <ControlButton action=Prev onPress currentPage totalPages />
      <Box spaceX=#xsmall>
        <Inline>
          {if currentPage > 1 {
            <PageButton action={Prev} onPress currentPage totalPages>
              {(currentPage - 1)->React.int}
            </PageButton>
          } else {
            React.null
          }}
          <PageButton onPress currentPage totalPages> {currentPage->React.int} </PageButton>
          {if currentPage < totalPages {
            <PageButton action={Next} onPress currentPage totalPages>
              {(currentPage + 1)->React.int}
            </PageButton>
          } else {
            React.null
          }}
        </Inline>
      </Box>
      <ControlButton action=Next onPress currentPage totalPages />
      <ControlButton action=Last onPress currentPage totalPages />
    </Inline>
  </View>

let make = React.memo(make)
