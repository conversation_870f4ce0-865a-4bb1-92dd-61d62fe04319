open Intl
open StyleX

let styles = StyleX.create({
  "view": style(
    ~display=#flex,
    ~flexDirection=#row,
    ~alignItems=#center,
    ~marginLeft="-" ++ Spaces.xsmallPx,
    (),
  ),
  "text": style(~marginLeft=Spaces.xxsmallPx, ~font=FontFaces.archivoSemiBold, ()),
  "defaultText": style(~color=Colors.neutralColor30, ()),
  "hoveredText": style(~color=Colors.neutralColor35, ()),
})

let textStyleFromParams = (~hovered) => hovered ? styles["hoveredText"] : styles["defaultText"]

@react.component
let make = () => {
  let (canGoBack, onGoBack) = Navigation.useGoBack()
  let (ref, hovered) = Hover.use()
  let hovered = hovered && canGoBack

  <Inline>
    <Touchable onPress={_ => onGoBack()} disabled={!canGoBack}>
      <View ref style={StyleX.props([styles["view"]])}>
        <Icon name=#back fill={hovered ? Colors.neutralColor50 : Colors.neutralColor25} />
        <span {...StyleX.props2([styles["text"], textStyleFromParams(~hovered)])}>
          {t("Back")->React.string}
        </span>
      </View>
    </Touchable>
  </Inline>
}

let make = React.memo(make)
