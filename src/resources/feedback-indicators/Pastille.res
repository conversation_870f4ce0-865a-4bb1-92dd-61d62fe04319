open StyleX

let styles = StyleX.create({
  "pastille": style(~width="7px", ~height="7px", ~borderRadius="50px", ()),
  "successView": style(~backgroundColor=Colors.successColor50, ()),
  "warningView": style(~backgroundColor=Colors.warningColor70, ()),
  "dangerView": style(~backgroundColor=Colors.dangerColor50, ()),
  "blankView": style(
    ~backgroundColor=Colors.neutralColor05,
    ~borderWidth="1px",
    ~borderColor=Colors.neutralColor25,
    (),
  ),
})

let viewStyleFromParams = (~variant, ~color) =>
  switch (variant, color) {
  | (_, Some(color)) => style(~backgroundColor=color, ())
  | (Some(#success), None) => styles["successView"]
  | (Some(#warning), None) => styles["warningView"]
  | (Some(#danger), None) => styles["dangerView"]
  | (None, None) => styles["blankView"]
  }

type variant = [#success | #warning | #danger]

@react.component
let make = (~variant=?, ~color=?) =>
  <View style={StyleX.props([styles["pastille"], viewStyleFromParams(~variant, ~color)])} />

let make = React.memo(make)
