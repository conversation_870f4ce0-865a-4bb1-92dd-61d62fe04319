open StyleX

let styles = StyleX.create({
  "root": style(~display=#flex, ~alignItems=#center, ~borderRadius="4px", ()),
  "text": style(~font=`normal 400 15px "Archivo"`, ~textAlign=#center, ~whiteSpace=#nowrap, ()),
  "normalTextView": style(~color=Colors.warningColor90, ()),
  "normalView": style(~backgroundColor=Colors.warningColor30, ()),
  "informationTextView": style(~color=Colors.secondaryColor70, ()),
  "informationView": style(~backgroundColor=Colors.secondaryColor05, ()),
  "successView": style(~backgroundColor=Colors.successColor10, ()),
  "successTextView": style(~color=Colors.successColor80, ()),
  "dangerView": style(~backgroundColor=Colors.dangerColor10, ()),
  "dangerTextView": style(~color=Colors.dangerColor55, ()),
  "primaryView": style(~backgroundColor=Colors.brandColor10, ()),
  "primaryTextView": style(~color=Colors.brandColor50, ()),
  "importantView": style(~backgroundColor=Colors.warningColor25, ()),
  "importantTextView": style(~color=Colors.Product.whiteTextColor, ()),
  "warningView": style(~backgroundColor=Colors.extraColorK70, ()),
  "warningTextView": style(~color=Colors.extraColorK80, ()),
  "neutralView": style(~backgroundColor=Colors.neutralColor20, ()),
  "neutralTextView": style(~color=Colors.neutralColor100, ()),
  "highlightedView": style(~backgroundColor=Colors.neutralColor00, ()),
  "lightText": style(~color=Colors.neutralColor00, ()),
  "darkText": style(~color=Colors.neutralColor80, ()),
})

let viewStyleFromParams = (~variation, ~highlighted, ~size, ~backgroundColor, ~borderColor) =>
  StyleX.arrayStyle([
    switch (variation, highlighted) {
    | (_, true) => styles["highlightedView"]
    | (#normal, false) => styles["normalView"]
    | (#success, false) => styles["successView"]
    | (#danger, false) => styles["dangerView"]
    | (#warning, false) => styles["warningView"]
    | (#primary, false) => styles["primaryView"]
    | (#neutral, false) => styles["neutralView"]
    | (#information, false) => styles["informationView"]
    | (#important, false) => styles["importantView"]
    | _ => style()
    },
    switch size {
    | #small =>
      style(~minHeight="16px", ~height="16px", ~paddingInline="4px", ~paddingBlock="2px", ())
    | #medium =>
      style(~minHeight="20px", ~height="20px", ~paddingInline="6px", ~paddingBlock="4px", ())
    | #large =>
      style(~minHeight="24px", ~height="24px", ~paddingInline="6px", ~paddingBlock="4px", ())
    },
    switch backgroundColor {
    | Some(backgroundColor) => style(~backgroundColor, ())
    | _ => style()
    },
    switch borderColor {
    | Some(borderColor) => style(~paddingBlock="3px", ~borderWidth="1px", ~borderColor, ())
    | _ => style()
    },
  ])

let textStyleFromParams = (~variation, ~highlighted, ~size, ~foregroundColor) =>
  StyleX.arrayStyle([
    switch (variation, highlighted) {
    | (#normal, _) => styles["normalTextView"]
    | (#success, _) => styles["successTextView"]
    | (#danger, _) => styles["dangerTextView"]
    | (#warning, _) => styles["warningTextView"]
    | (#neutral, _) => styles["neutralTextView"]
    | (#primary, _) => styles["primaryTextView"]
    | (#information, _) => styles["informationTextView"]
    | (#important, _) => styles["importantTextView"]
    | _ => style()
    },
    switch size {
    | #xsmall | #small => style(~fontSize="10px", ())
    | #medium => style(~fontSize="12px", ())
    | #large => style(~fontSize="14px", ())
    },
    switch foregroundColor {
    | Some(color) => style(~color, ())
    | _ => style()
    },
  ])

type size = [#small | #medium | #large]

type variation = [
  | #normal
  | #success
  | #danger
  | #warning
  | #primary
  | #neutral
  | #information
  | #important
]

@react.component
let make = (
  ~children,
  ~highlighted=false,
  ~size: size=#large, // TODO - as new design system gets integrated, will later on be #medium as default
  ~variation=#normal,
  ~borderColor=?,
  ~backgroundColor=?,
  ~foregroundColor=?,
) =>
  <View
    style={StyleX.props([
      styles["root"],
      viewStyleFromParams(~variation, ~size, ~highlighted, ~backgroundColor, ~borderColor),
    ])}>
    <span
      {...StyleX.props2([
        styles["text"],
        textStyleFromParams(~variation, ~size, ~highlighted, ~foregroundColor),
      ])}>
      children
    </span>
  </View>

let make = React.memo(make)
