open StyleX

let styles = StyleX.create({
  "view": style(
    ~alignSelf=#"flex-start",
    ~paddingBlock="4px",
    ~paddingInline="8px",
    ~borderRadius="3px",
    ~backgroundColor=Colors.neutralColor15,
    (),
  ),
  "text": style(
    ~marginTop="1.5px",
    ~font=`normal 400 13px "Libre Franklin"`,
    ~color=Colors.neutralColor100,
    ~textAlign=#center,
    (),
  ),
})
@react.component
let make = (~children) =>
  <View style={StyleX.props([styles["view"]])}>
    <span {...StyleX.props2([styles["text"]])}> children </span>
  </View>

let make = React.memo(make)
