open StyleX

let styles = StyleX.create({
  "normalText": style(~font=`normal 400 15px "Libre Franklin"`, ()),
  "importantText": style(~font=`normal 600 15px "Archivo"`, ()),
  "lightText": style(~color=Colors.neutralColor20, ()),
  "grayText": style(~color=Colors.neutralColor100, ()),
  "darkText": style(~color=Colors.brandColor60, ()),
})

let iconSizeFromParams = (~size) =>
  switch size {
  | #tiny | #xxsmall | #xsmall => 16.
  | #small => 18.
  | #normal | _ => 20.
  }

let iconColorFromParams = (~disabled, ~hovered) =>
  switch (disabled, hovered) {
  | (true, _) => Colors.neutralColor20
  | (false, true) => Colors.brandColor60
  | (false, _) => Colors.neutralColor100
  }

let textStyleFromParams = (~variation, ~size, ~disabled, ~hovered) =>
  StyleX.props2([
    switch variation {
    | #normal => styles["normalText"]
    | #important => styles["importantText"]
    },
    switch size {
    | #tiny | #xxsmall | #xsmall => style(~fontSize=#xsmall->FontSizes.toPx, ())
    | #small => style(~fontSize=#small->FontSizes.toPx, ())
    | #normal | _ => style(~fontSize=#large->FontSizes.toPx, ())
    },
    switch (disabled, hovered) {
    | (true, _) => styles["lightText"]
    | (false, true) => styles["darkText"]
    | (false, _) => styles["grayText"]
    },
  ])

type variation = [#normal | #important]

@react.component
let make = React.forwardRef((
  ~children,
  ~variation=#important,
  ~size=#normal,
  ~disabled=false,
  ~icon,
  ~textTooltip=?,
  ~onPress,
  ref,
) => {
  let (ref, hovered) = Hover.use(~ref=?ref->Js.Nullable.toOption, ())

  <Touchable ref disabled onPress>
    <Inline space=#xsmall>
      <Icon
        name=icon size={iconSizeFromParams(~size)} fill={iconColorFromParams(~disabled, ~hovered)}
      />
      <span {...textStyleFromParams(~variation, ~size, ~disabled, ~hovered)}> children </span>
      {switch textTooltip {
      | Some(textTooltip) =>
        <TooltipIcon variation=#info altTriggerRef=ref>
          <Tooltip.Span text=textTooltip />
        </TooltipIcon>
      | None => React.null
      }}
    </Inline>
  </Touchable>
})
