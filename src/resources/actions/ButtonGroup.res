open StyleX

let styles = StyleX.create({
  "item": style(~width="100%", ~marginLeft=Spaces.smallPx, ()),
})

@react.component
let make = (~children) =>
  <Inline space=#xsmall align=#center>
    {children
    ->React.Children.toArray
    ->Array.mapWithIndex((index, child) =>
      <View key={index->Int.toString} style={StyleX.props([styles["item"]])}> child </View>
    )
    ->React.array}
  </Inline>

let make = React.memo(make)
