open StyleX

let styles = StyleX.create({
  "main": style(
    ~font=`normal 600 15px "Libre Franklin"`,
    ~color=Colors.neutralColor90,
    ~letterSpacing="0.125px",
    (),
  ),
  "highlighted": style(~color=Colors.brandColor50, ()),
  "hovered": style(~color=Colors.brandColor50, ~textDecorationLine=#underline, ()),
})

let styleFromParams = (~highlighted, ~hovered, ~size) =>
  StyleX.props2([
    styles["main"],
    switch (highlighted, hovered) {
    | (true, false) => styles["highlighted"]
    | (_, true) => styles["hovered"]
    | (false, false) => style()
    },
    switch size {
    | #normal => style(~fontSize=#normal->FontSizes.toPx, ())
    | #compact => style(~fontSize=#xsmall->FontSizes.toPx, ())
    },
  ])

type size = [#normal | #compact]

@react.component
let make = (~text, ~size=#normal, ~highlighted=false, ~onPress) => {
  let (ref, hovered) = Hover.use()

  <Touchable wrap=false onPress={_ => onPress()}>
    <span {...styleFromParams(~size, ~highlighted, ~hovered)} ref={ref->ReactDOM.Ref.domRef}>
      {text->React.string}
    </span>
  </Touchable>
}
