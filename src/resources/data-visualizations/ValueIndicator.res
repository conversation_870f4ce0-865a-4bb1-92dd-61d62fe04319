open Intl
open StyleX

let styles = StyleX.create({
  "standardView": style(~font=`normal 700 26px "Archivo"`, ~color=Colors.neutralColor90, ()),
  "compactView": style(~font=`normal 600 16px "Archivo"`, ~color=Colors.neutralColor90, ()),
})

type variant = [
  | #standard
  | #compact
]

type value = [
  | #currency(float, Intl.currency)
  | #percent(float)
  | #decimal(float)
  | #integer(int)
]

let currencyValue = (value, ~currency) => #currency(value, currency)
let percentValue = value => #percent(value)
let decimalValue = value => #decimal(value)
let integerValue = value => #integer(value)

let styleProps = (~variant) =>
  StyleX.props2([
    switch variant {
    | #standard => styles["standardView"]
    | #compact => styles["compactView"]
    },
  ])

let formatValue = value =>
  switch value {
  | #currency(value, currency) =>
    value->currencyFormat(~currency, ~minimumFractionDigits=2, ~maximumFractionDigits=2)
  | #percent(value) => value->percentFormat(~minimumFractionDigits=2, ~maximumFractionDigits=2)
  | #decimal(value) => value->decimalFormat(~minimumFractionDigits=2, ~maximumFractionDigits=2)
  | #integer(value) =>
    value->Float.fromInt->decimalFormat(~minimumFractionDigits=0, ~maximumFractionDigits=0)
  }

@react.component
let make = (~value, ~variant=#standard) => {
  let formattedValue = formatValue(value)
  <span {...styleProps(~variant)}> {formattedValue->React.string} </span>
}

let make = React.memo(make)

React.setDisplayName(make, "ValueIndicator")
