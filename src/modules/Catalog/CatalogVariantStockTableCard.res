open Intl
open StyleX

module Config = CatalogVariant__Config
module Row = {
  type t = Config.variantStockInformation
  let keyExtractor = row => row.Config.shopId
}

let columns = [
  {
    Table.key: "shopname",
    name: t("Shop/Warehouse"),
    layout: {minWidth: 150.->#px, margin: #normal},
    render: ({data: {Config.shopName: shopName}}) =>
      <Box spaceY=#xxsmall>
        <TextStyle> {shopName->React.string} </TextStyle>
      </Box>,
  },
  {
    key: "stock-quantity",
    name: "Stock",
    layout: {width: 0.->#fr},
    render: ({data: {formattedQuantity, state}}) =>
      <ProductStockTableCell value=formattedQuantity ?state />,
  },
]

let styles = StyleX.create({
  "multishopStockText": style(~font=`normal 700 30px "Archivo"`, ()),
})

type modalsOpened = {
  loss: bool,
  delivery: bool,
}

let initialModalsState = {
  loss: false,
  delivery: false,
}

@react.component
let make = (~cku, ~shopsVariantStock) => {
  let (reportingModalsOpened, setReportingModalsOpened) = React.useState(() => initialModalsState)
  let multishop = switch Auth.useScope() {
  | Organisation({activeShop: None}) => true
  | _ => false
  }
  let shopsStockDiffering =
    multishop &&
    shopsVariantStock
    ->CatalogVariant.MultiShops.arrayKeepUniqueBy((
      a: Config.variantStockInformation,
      b: Config.variantStockInformation,
    ) => a.capacityUnit === b.capacityUnit && a.capacityPrecision === b.capacityPrecision)
    ->Array.length > 1

  let makeMultishopStock = React.useCallback1((capacityUnit, capacityPrecision) => {
    let totalStock = shopsVariantStock->Array.reduce(0, (acc, stock) => acc + stock.rawQuantity)

    totalStock->CatalogVariant.StockQuantity.format(~capacityPrecision, ~capacityUnit)
  }, [shopsVariantStock])

  let onRequestClose = React.useCallback0(() => setReportingModalsOpened(_ => initialModalsState))

  <>
    <Card variation=#table title={multishop ? "Stocks" : "Stock"}>
      <Stack space=#small>
        <Box spaceX=#large>
          {switch (multishop, shopsStockDiffering, shopsVariantStock[0]) {
          | (true, false, Some({capacityUnit, capacityPrecision})) =>
            <Box spaceBottom=#medium>
              <span {...StyleX.props2([styles["multishopStockText"]])}>
                {makeMultishopStock(capacityUnit, capacityPrecision)->React.string}
              </span>
            </Box>
          | _ => React.null
          }}
          <Inline space=#small>
            <Button
              variation=#danger
              size=#xsmall
              onPress={_ => setReportingModalsOpened(_ => {loss: true, delivery: false})}>
              {t("Loss")->React.string}
            </Button>
            <Button
              variation=#neutral
              size=#xsmall
              onPress={_ => setReportingModalsOpened(_ => {loss: false, delivery: true})}>
              {t("Delivery")->React.string}
            </Button>
          </Inline>
        </Box>
        <TableView
          columns data=AsyncData.Done(Ok(shopsVariantStock)) keyExtractor=Row.keyExtractor
        />
      </Stack>
    </Card>
    <CatalogVariantLossReportModal
      cku shopsVariantStock opened=reportingModalsOpened.loss onRequestClose
    />
    <CatalogVariantDeliveryReportModal
      cku shopsVariantStock opened=reportingModalsOpened.delivery onRequestClose
    />
  </>
}

let make = React.memo(make)
