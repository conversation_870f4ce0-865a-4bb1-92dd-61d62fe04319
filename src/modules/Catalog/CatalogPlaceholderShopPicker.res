open Intl
open StyleX

@react.component
let make = (~children, ~message, ~disabledIds=[], ~renderItemContent=?) => {
  let shops = Auth.useShops()
  let activeShop = Auth.useActiveShop()

  let placeholderChildComponent = React.useCallback2(() => {
    let dispatch = Auth.useDispatch()

    let sections = [
      {
        Select.items: shops->Array.map(shop => {
          Select.key: shop.id,
          label: shop.name,
          value: Some(shop),
          disabled: disabledIds->Array.some(id => id === shop.id),
        }),
      },
    ]

    <View style={StyleX.props([style(~alignSelf=#center, ())])}>
      <Select
        preset=#inputField({required: true})
        placeholder={t("Select a shop")}
        ?renderItemContent
        sections
        value=activeShop
        onChange={value => ActiveShopSet(value)->dispatch}
      />
    </View>
  }, (shops, disabledIds))

  switch (shops, activeShop) {
  | ([], _) => <Placeholder status=Loading />
  | (_, None) =>
    <Placeholder
      status=Pending({
        illustration: Some(Illustration.shopMissing),
        title: t("Beware !"),
        message,
      })
      childComponent=placeholderChildComponent
    />
  | _ => children
  }
}

let make = React.memo(make)
