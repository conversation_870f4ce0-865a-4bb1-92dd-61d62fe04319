open Intl

module Config = CatalogProduct__Config

module Row = {
  type t = Config.variant
  let keyExtractor = row => row.Config.id ++ "-variant"
}

module RetailPriceDetailsCell = {
  @react.component
  let make = React.memo((~formattedRetailPrice, ~retailPrices: array<Config.variantPrice>) => {
    let ref = React.useRef(Js.Nullable.null)

    <View ref>
      <Inline space=#small>
        <TextStyle> {formattedRetailPrice->React.string} </TextStyle>
        {switch retailPrices {
        | [] => React.null
        | _ =>
          <TooltipIcon variation=#info altTriggerRef=ref>
            {retailPrices
            ->Array.map(({name, formattedValue, taxIncluded, default}) => {
              let formattedTaxType = t(taxIncluded ? "VAT incl." : "VAT excl.")
              let formattedPrice = `${name} ${formattedTaxType} :  ${formattedValue}\n`
              <>
                <Tooltip.Span bold=default text=formattedPrice />
                <Tooltip.Gap space=#xxsmall />
              </>
            })
            ->React.array}
          </TooltipIcon>
        }}
      </Inline>
    </View>
  })
}

let tableColumns = (~allShopsFiltered) => [
  {
    Table.key: "variant-name",
    name: t("Name"),
    layout: {minWidth: 170.->#px, width: 1.5->#fr},
    render: ({data: {Config.cku: cku, name, shopName}}) => {
      let shopName = allShopsFiltered ? Some(shopName) : None
      <CatalogProductNameTableCell cku name ?shopName />
    },
  },
  {
    key: "variant-purchaseprice",
    name: t("Purchase price"),
    layout: {minWidth: 120.->#px},
    render: ({data: {id, formattedPurchasePrice, bulkUnit, purchasePrice}}) =>
      switch formattedPurchasePrice {
      | Some("VARIABLE") =>
        <TextStyle variation=#normal size=#small> {t("Differing")->React.string} </TextStyle>
      | _ =>
        <PricePurchaseTableEditCellWrapper
          value=purchasePrice formattedPurchasePrice bulkUnit variantId=id
        />
      },
  },
  {
    key: "variant-retailprice",
    name: t("Retail price"),
    layout: {minWidth: 120.->#px},
    render: ({data: {retailPrices, formattedRetailPrice}}) =>
      <RetailPriceDetailsCell formattedRetailPrice retailPrices />,
  },
  {
    key: "variant-stock",
    name: t("Stock"),
    layout: {minWidth: 120.->#px},
    render: ({
      data: {
        stock,
        shopName,
        id,
        maxStockThreshold,
        minStockThreshold,
        stockOrderTriggerThreshold,
        bulk,
      },
    }) =>
      <CatalogVariantStockThresholdCell
        stockQuantity=stock.quantity
        stockState=stock.state
        formattedShopsNames=Some(shopName)
        id
        maxStockThreshold
        minStockThreshold
        stockOrderTriggerThreshold
        bulk
      />,
  },
  {
    key: "variant-status",
    name: t("Status"),
    render: ({data: {status}}) =>
      switch status {
      | Some(status) => <CatalogProductStatusBadge value=Some(status) />
      | _ => <TextStyle variation=#normal> {t("Differing")->React.string} </TextStyle>
      },
  },
  {
    key: "variant-actions",
    layout: {width: 0.->#fr, alignX: #flexEnd},
    render: ({data: {cku, id, status}}) => <CatalogTableActions cku id ?status />,
  },
]

@react.component
let make = (~shopsProductVariants) => {
  let allShopsFiltered = switch Auth.useScope() {
  | Organisation({activeShop: None}) => true
  | _ => false
  }
  let columns = React.useMemo1(() => tableColumns(~allShopsFiltered), [allShopsFiltered])
  let rows = Array.concatMany(shopsProductVariants)

  <Card variation=#table grow=true title={t("Product's variants")}>
    <Box spaceTop=#small>
      <TableView
        columns
        data=AsyncData.Done(Ok(rows))
        keyExtractor=Row.keyExtractor
        hideCard=true
        maxHeight=500.
        placeholderEmptyState={<Box spaceX=#large spaceY=#xlarge>
          <Inline align=#center>
            <TextStyle variation=#normal size=#small align=#center>
              {t("No product variant has been yet recorded")->React.string}
            </TextStyle>
          </Inline>
        </Box>}
      />
    </Box>
  </Card>
}

let make = React.memo(make)
