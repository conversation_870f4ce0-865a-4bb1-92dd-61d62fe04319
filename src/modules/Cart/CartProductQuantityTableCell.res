open Intl
open StyleX
open Accounting.Actions
open Accounting.Types

let styles = StyleX.create({
  "wrapper": style(~display=#flex, ~alignItems=#center, ()),
})

@react.component
let make = (
  ~product,
  ~editable=false,
  ~beforeOrderReception=true,
  ~onRequestDispatch as dispatch,
) => {
  // REVIEW - maybe add an Accounting helper to get the 4 'value in float
  let (productId, packaging, quantity, expectedQuantity, expectedQuantityWarning) = switch product {
  | Unit(product) => (
      product.id,
      product.packaging->Option.map(Int.toFloat),
      product.quantity->Int.toFloat,
      product.expectedQuantity->Int.toFloat,
      product.expectedQuantityWarning,
    )
  | Bulk(product, _) => (
      product.id,
      product.packaging->Option.map(Big.toFloat),
      product.quantity->Big.toFloat,
      product.expectedQuantity->Big.toFloat,
      product.expectedQuantityWarning,
    )
  }
  // REVIEW - maybe add this logic in ab Accounting helper
  let bulk = switch product {
  | Bulk({capacityUnit: Some(unit)}, precision) => Some((unit, precision))
  | _ => None
  }

  let onChange = React.useCallback1(value => {
    // REVIEW - maybe add this logic in an Accounting helper
    let quantity = switch product->Accounting.isBulk {
    | true => BulkQuantity(value)
    | false => UnitQuantity(value->Float.toInt)
    }

    // Modifies both quantity and 'expected before #RECEIVING step
    switch beforeOrderReception {
    | true => ProductExpectedQuantityUpdated(productId, quantity)->dispatch
    | _ => ProductQuantityUpdated(productId, quantity)->dispatch
    }
  }, [productId])

  // REVIEW - add as Accounting helper (returns (bulk: bool, precision: int))
  let inputPrecision = switch bulk {
  | Some((_, precision)) => precision
  | _ => 0
  }
  let inputAppender: option<InputNumberField.appender> = switch (bulk, beforeOrderReception) {
  | (None, false) => Some(Custom(`/ ${expectedQuantity->Float.toString}`))
  | (Some((unit, _)), false) => Some(Custom(`/ ${expectedQuantity->Float.toString} ${unit}`))
  | (Some((unit, _)), _) => Some(Custom(` ${unit}`))
  | _ => None
  }
  let formattedQuantity =
    quantity->Float.toString->Js.String2.replace(".", ",") ++
      bulk->Option.mapWithDefault("", ((unit, _)) => ` ${unit}`)

  <View style={StyleX.props([styles["wrapper"]])}>
    {switch editable {
    | true =>
      <>
        <Box spaceRight=#xnormal>
          <InputNumberField
            appender=?inputAppender
            minValue=0.
            precision=inputPrecision
            shrinkInput=true
            value={quantity}
            onChange
          />
        </Box>
        {switch (packaging, expectedQuantityWarning) {
        | (Some(packaging), warning) if warning->Array.size > 0 =>
          let formattedPackagingBy = template(
            t("Packaging by {{packaging}}"),
            ~values={"packaging": packaging},
            (),
          )
          <TooltipIcon key=productId variation=#info>
            <Tooltip.Span text=formattedPackagingBy />
          </TooltipIcon>
        | _ => React.null
        }}
      </>
    | _ => <TextStyle weight=#semibold> {formattedQuantity->React.string} </TextStyle>
    }}
  </View>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.product === newProps.product &&
  oldProps.editable === newProps.editable &&
  oldProps.beforeOrderReception === newProps.beforeOrderReception
)
