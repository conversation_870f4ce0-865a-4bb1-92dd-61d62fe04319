open Intl
open Accounting.Types
open Accounting.Actions

module Utils = CartDiscount__Utils

let makeDefaultDiscount = (~product) => {
  let quantity = switch product {
  | Unit({quantity}) => quantity->Float.fromInt
  | Bulk({quantity}, _) => quantity->Big.toFloat
  }

  {
    id: "",
    name: "",
    kind: Percent,
    value: 0.,
    quantity: quantity->Big.fromFloat,
    formattedValue: None,
    amount: None,
    formattedAmount: None,
    warnings: [],
  }
}

@react.component
let make = (
  ~product,
  ~discount=makeDefaultDiscount(~product),
  ~onPressRemoveDiscount,
  ~onRequestDispatch as dispatch,
) => {
  let (value, setValue) = React.useState(() => discount.value)

  let (productId, formattedTotalPrice, formattedTotalLocalDiscounts) = switch product {
  | Unit({id, formattedTotalPrice, totalLocalDiscounts})
  | Bulk({id, formattedTotalPrice, totalLocalDiscounts}, _) =>
    let formattedTotalLocalDiscounts =
      totalLocalDiscounts->Option.map(value =>
        Big.toFloat(value)->Intl.decimalFormat(~minimumFractionDigits=3, ~maximumFractionDigits=3)
      )
    (id, formattedTotalPrice, formattedTotalLocalDiscounts)
  }
  let (productQuantity, capacityPrecision) = switch product {
  | Unit({quantity}) => (quantity->Float.fromInt, 0)
  | Bulk({quantity}, precision) => (quantity->Big.toFloat, precision)
  }
  let totalPrice = switch product {
  | Unit({totalPrice: Some(totalPrice)})
  | Bulk({totalPrice: Some(totalPrice)}, _) =>
    totalPrice->Big.toFloat
  | _ => 0.
  }

  let discountExists = discount.id !== ""

  ReactUpdateEffect.use1(() => {
    if !discountExists && value > 0. {
      ProductDiscountAdded(
        productId,
        {
          id: None,
          name: "",
          kind: Percent,
          value,
          quantity: 0,
        },
      )->dispatch
    } else {
      ProductDiscountUpdated(
        productId,
        discount.id,
        switch discount.kind {
        | Percent => {
            ...discount,
            value: value > 100. ? 100. : value,
          }
        | Currency => {
            ...discount,
            value: value > totalPrice ? totalPrice : value,
          }
        | Free => {
            ...discount,
            quantity: value > productQuantity
              ? productQuantity->Big.fromFloat
              : value->Big.fromFloat,
          }
        },
      )->dispatch
    }
    None
  }, [value])

  let onDiscountKindChange = React.useCallback1(kind => {
    if !discountExists {
      ProductDiscountAdded(
        productId,
        {
          id: None,
          name: "",
          kind,
          value: 0.,
          quantity: 0,
        },
      )->dispatch
    } else {
      ProductDiscountUpdated(productId, discount.id, {...discount, kind, value: 0.})->dispatch
    }
  }, [discount])

  let onPressRemoveDiscount = React.useCallback1(_ => {
    ProductDiscountRemoved(productId, discount.id)->dispatch
    onPressRemoveDiscount()
  }, [discount.id])

  <View>
    <Stack space=#medium>
      <Group wrap=false grid=["auto", "1fr"] spaceX=#medium>
        <InputSegmentedControlsField
          label={t("Discount type")}
          compact=true
          required=false
          options=list{Percent, Currency, Free}
          optionToText=Utils.kindToText
          value=discount.kind
          onChange=onDiscountKindChange
        />
        {switch discount.kind {
        | Percent =>
          <InputNumberField
            label={t("Discount percentage")}
            appender=Percent
            minValue=0.
            maxValue=100.
            precision=2
            value
            onChange={value => setValue(_ => value)}
          />
        | Currency =>
          <Stack space=#xsmall>
            <InputNumberField
              label={t("Discount amount")}
              appender=Currency(#EUR)
              minValue=0.
              maxValue=totalPrice
              value
              onChange={value => setValue(_ => value)}
            />
            {switch discount.warnings {
            | [warning] =>
              <Box spaceX=#xsmall>
                <TextStyle size=#xsmall>
                  {template(
                    warning->Utils.discountWarningToText,
                    ~values={"max": formattedTotalPrice},
                    (),
                  )->React.string}
                </TextStyle>
              </Box>
            | _ => React.null
            }}
          </Stack>
        | Free =>
          let inputAppender = switch product {
          | Bulk({capacityUnit: Some(unit)}, _) =>
            InputNumberField.Custom(
              `/ ${productQuantity->Float.toString->Js.String2.replace(".", ",")} ${unit}`,
            )
          | _ => Custom(`/ ${productQuantity->Float.toString}`)
          }

          <InputNumberField
            label={t("Quantity offered")}
            appender=inputAppender
            minValue=0.
            maxValue={productQuantity}
            precision=capacityPrecision
            value={discount.quantity->Big.toFloat}
            onChange={value => setValue(_ => value)}
          />
        }}
      </Group>
      <Stack space=#xxsmall>
        <Strong>
          {template(
            t("Total discount : {{amount}}"),
            ~values={
              "amount": formattedTotalLocalDiscounts->Option.getExn,
            },
            (),
          )->React.string}
        </Strong>
        <Box spaceY=#xmedium spaceBottom=#none>
          <TextIconButton icon=#delete_light onPress=onPressRemoveDiscount>
            {t("Remove discount")->React.string}
          </TextIconButton>
        </Box>
      </Stack>
    </Stack>
  </View>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.product === newProps.product && oldProps.discount === newProps.discount
)
