open Intl
open StyleX
open PromotionEditForm

module Row = {
  type t = RootCampaign.t
  let keyExtractor = row => row.RootCampaign.id
}

let tableColumns = (~editing, ~promotionCreated) => [
  {
    Table.key: "shopName",
    name: t("Shop"),
    layout: {minWidth: 150.->#px},
    render: ({data: {RootCampaign.shopName: shopName}}) =>
      <Box spaceY=#normal>
        <TextStyle> {shopName->React.string} </TextStyle>
      </Box>,
  },
  {
    key: "status",
    name: t("Status"),
    layout: {minWidth: 120.->#px},
    render: ({data: {status}}) =>
      switch status {
      | Some(status) => <PromotionStatusBadge status />
      | _ => React.null
      },
  },
  {
    key: "action",
    layout: {
      alignX: #flexEnd,
      width: 0.->#fr,
      hidden: !promotionCreated || editing,
    },
    render: ({data: {id, status}}) =>
      switch status {
      | Some(status) => <PromotionEditActions variation=#menuItem id status />
      | _ => React.null
      },
  },
]

module PricesQuery = %graphql(`
  query PricesQuery($after: String) {
    prices(first: 50, after: $after) {
      pageInfo {
        endCursor
        hasNextPage
      }
      edges {
        node {
          id
          name
          shop {
            id
          }
        }
      }
    }
  }
`)

type queryResult = Js.Promise.t<
  Result.t<
    ApolloClient__React_Hooks_UseApolloClient.ApolloClient.ApolloQueryResult.t__ok<PricesQuery.t>,
    ApolloClient__React_Hooks_UseApolloClient.ApolloClient.ApolloError.t,
  >,
>
type data = PricesQuery.t_prices_edges

type status =
  | Loading
  | Success(array<data>)
  | Error

// TODO - usePaginate https://github.com/winoteam/pos/pull/416#discussion_r801739276
let rec runScanPrices = (
  ~pricesFetch: (~after: option<string>) => queryResult,
  ~cursor: option<string>=?,
  ~data: array<data>=[],
  ~onDone: status => unit,
  (),
) =>
  pricesFetch(~after=cursor)
  ->FuturePromise.fromPromise
  ->Future.mapOk(response =>
    switch response {
    | Ok({
        data: {prices: {pageInfo: {endCursor, hasNextPage: Some(true)}, edges: prices}},
        error: None,
      }) =>
      runScanPrices(~pricesFetch, ~data=data->Array.concat(prices), ~cursor=?endCursor, ~onDone, ())
    | Ok({data: {prices: {edges: prices}}, error: None}) =>
      onDone(Success(data->Array.concat(prices)))
    | _ => onDone(Error)
    }
  )
  ->ignore

let makeEligibleShopsFromPrices = (
  ~prices: array<data>,
  ~shops: array<Auth.shop>,
  ~selectedPriceName: string,
) =>
  shops->Array.keepMap(shop =>
    switch prices->Array.getBy(({node: price}) =>
      price.shop.id === shop.id && price.name === selectedPriceName && shop.kind != #WAREHOUSE
    ) {
    | Some({node: price}) =>
      Some({
        RootCampaign.id: Uuid.make()->Uuid.toString,
        creatorIdentifier: "",
        shopId: shop.id,
        shopName: shop.name,
        priceId: price.id,
        status: None,
        selected: false,
      })
    | _ => None
    }
  )

@react.component
let make = (~editing, ~promotionCreated, ~campaigns, ~selectedPriceName, ~onRequestShopsUpdate) => {
  let shops = Auth.useShops()
  let apolloClient = ApolloClient.React.useApolloClient()
  let (status, setStatus) = React.useState(() => Loading)

  // Fetches combined prices data from every shops
  React.useEffect0(() => {
    let pricesFetch = (~after) =>
      apolloClient.query(
        ~query=module(PricesQuery),
        ~fetchPolicy=NetworkOnly,
        PricesQuery.makeVariables(~after?, ()),
      )

    runScanPrices(~pricesFetch, ~onDone=result => setStatus(_ => result), ())

    Some(() => onRequestShopsUpdate([]))
  })

  let columns = React.useMemo2(
    () => tableColumns(~promotionCreated, ~editing),
    (promotionCreated, editing),
  )

  // Gets shops associated to the selected price name
  let rows = React.useMemo2(() =>
    switch (status, promotionCreated, campaigns) {
    | (Success(prices), false, []) =>
      makeEligibleShopsFromPrices(~prices, ~shops, ~selectedPriceName)
    | _ => campaigns
    }
  , (status, campaigns))

  // Updates rows with selected status upon table selection
  let onSelectChange = React.useCallback1(selectedRowsKeys =>
    onRequestShopsUpdate(
      switch selectedRowsKeys {
      | Table.Selected(keys) =>
        rows->Array.map(campaign =>
          keys->Array.some(key => key === campaign.id)
            ? {...campaign, selected: true}
            : {...campaign, selected: false}
        )
      | All => rows->Array.map(campaign => {...campaign, selected: true})
      },
    )
  , [rows])

  <View
    style={switch Auth.useScope() {
    | Single(_) => StyleX.props([style(~opacity=0., ~width="0px", ())])
    | _ => StyleX.props([style(~width="100%", ())])
    }}>
    <Card variation=#table grow=true title={t("Campaign management by shop")}>
      {switch promotionCreated {
      | false =>
        <Box spaceX=#large spaceTop=#small spaceBottom=#normal>
          <TextStyle variation=#normal>
            {t(
              "Once the draft is saved, you will no longer be able to edit the selection.",
            )->React.string}
          </TextStyle>
        </Box>
      | _ => React.null
      }}
      {if rows->Array.length === 0 {
        <Placeholder status=Loading />
      } else {
        <TableView
          columns
          data=AsyncData.Done(Ok(rows))
          keyExtractor=Row.keyExtractor
          selectionEnabled={!promotionCreated}
          initialAllSelected={!promotionCreated}
          maxHeight=292.
          onSelectChange
        />
      }}
    </Card>
  </View>
}

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.campaigns === newProps.campaigns &&
  oldProps.editing === newProps.editing &&
  oldProps.promotionCreated === newProps.promotionCreated &&
  oldProps.selectedPriceName === newProps.selectedPriceName
)
