open StyleX

let truncateWithMiddleEllipsis = (str, ~maxLen) =>
  if str->String.length > maxLen {
    let len = str->String.length
    let halfLen = (maxLen - 3) / 2
    let startStr = str->Js.String2.slice(~from=0, ~to_=halfLen)->Js.String2.trim
    let endStr = str->Js.String2.slice(~from=len - halfLen, ~to_=len)->Js.String2.trim
    startStr ++ "..." ++ endStr
  } else {
    str
  }

@react.component
let make = (
  ~value: option<string>,
  ~state: option<Product.Stock.t>=?,
  ~size: FontSizes.t=#normal,
  ~formattedShopsNames: option<string>=?,
) =>
  switch value {
  | Some(value) =>
    <View style={StyleX.props([style(~display=#flex, ~flexDirection=#column, ())])}>
      <Stack space=#none>
        <Inline space=#none>
          {switch state {
          | Some(state) =>
            <Pastille
              variant={switch state {
              | #OK => #success
              | #ALERT => #warning
              | #DANGER => #danger
              }}
            />
          | None => React.null
          }}
          <Box spaceRight=#xsmall />
          <Box spaceRight=#xxsmall />
          <TextStyle size> {value->React.string} </TextStyle>
        </Inline>
        {switch formattedShopsNames {
        | Some(shopsName) =>
          let formattedShopsName = truncateWithMiddleEllipsis(shopsName, ~maxLen=15)
          <TextStyle size=#tiny variation=#normal maxLines=1>
            {formattedShopsName->React.string}
          </TextStyle>
        | _ => React.null
        }}
      </Stack>
    </View>
  | _ => <TextStyle size> {"—"->React.string} </TextStyle>
  }

let make = React.memo(make)
