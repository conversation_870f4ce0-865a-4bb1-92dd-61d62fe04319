open StyleX
open Intl

type badge = {
  variation: Badge.variation,
  text: string,
}

module TooltipIconTrigger = {
  let styles = StyleX.create({
    "root": style(~display=#flex, ~zIndex=1, ~cursor=#auto, ()),
  })
  let styleProps = () => StyleX.props([styles["root"]])

  @react.component
  let make = (~information) => {
    let (tooltipOpened, setTooltipOpened) = React.useState(() => false)

    let content =
      <>
        <Tooltip.Span text={t("Name") ++ t(":") ++ " "} tone=#faded />
        <Tooltip.Span
          text={information.CatalogProduct.Information.productName ++
          " | " ++
          information.variantName}
        />
        <Tooltip.Gap space=#xxsmall />
        {information.sku->Option.mapWithDefault(React.null, sku => <>
          <Tooltip.Span text={t("SKU code") ++ t(":") ++ " "} tone=#faded />
          <Tooltip.Span text=sku />
          <Tooltip.Gap space=#xxsmall />
        </>)}
        {information.plu->Option.mapWithDefault(React.null, plu => <>
          <Tooltip.Span text={t("PLU code") ++ t(":") ++ " "} tone=#faded />
          <Tooltip.Span text=plu />
          <Tooltip.Gap space=#xxsmall />
        </>)}
        {information.internalCode->Option.mapWithDefault(React.null, internalCode => <>
          <Tooltip.Span text={t("Internal code") ++ t(":") ++ " "} tone=#faded />
          <Tooltip.Span text=internalCode />
          <Tooltip.Gap space=#xxsmall />
        </>)}
        <Tooltip.Gap space=#small />
        {information.color->Option.mapWithDefault(React.null, color => <>
          <Tooltip.Span text={t("Color") ++ t(":") ++ " "} tone=#faded />
          <Tooltip.Span text={CatalogProduct.Color.toLabel(color)} />
          <Tooltip.Gap space=#xxsmall />
        </>)}
        {information.producerName->Option.mapWithDefault(React.null, producer => <>
          <Tooltip.Span text={t("Producer") ++ t(":") ++ " "} tone=#faded />
          <Tooltip.Span text=producer />
          <Tooltip.Gap space=#xxsmall />
        </>)}
        {information.supplierName->Option.mapWithDefault(React.null, supplier => <>
          <Tooltip.Span text={t("Supplier") ++ t(":") ++ " "} tone=#faded />
          <Tooltip.Span text=supplier />
          <Tooltip.Gap space=#xxsmall />
        </>)}
        {information.designation->Option.mapWithDefault(React.null, designation => <>
          <Tooltip.Span text={t("Designation") ++ t(":") ++ " "} tone=#faded />
          <Tooltip.Span text=designation />
          <Tooltip.Gap space=#xxsmall />
        </>)}
        {information.productFamily->Option.mapWithDefault(React.null, productFamily => <>
          <Tooltip.Span text={t("Family") ++ t(":") ++ " "} tone=#faded />
          <Tooltip.Span text=productFamily />
          <Tooltip.Gap space=#xxsmall />
        </>)}
        {information.beerType->Option.mapWithDefault(React.null, beerType => <>
          <Tooltip.Span text={t("Beer type") ++ t(":") ++ " "} tone=#faded />
          <Tooltip.Span text=beerType />
          <Tooltip.Gap space=#xxsmall />
        </>)}
        {information.wineType->Option.mapWithDefault(React.null, wineType => <>
          <Tooltip.Span text={t("Wine type") ++ t(":") ++ " "} tone=#faded />
          <Tooltip.Span text={CatalogProduct.WineType.toLabel(wineType)} />
          <Tooltip.Gap space=#xxsmall />
        </>)}
        {information.whiteWineType->Option.mapWithDefault(React.null, whiteWineType => <>
          <Tooltip.Span text={t("White wine type") ++ t(":") ++ " "} tone=#faded />
          <Tooltip.Span text={CatalogProduct.WhiteWineType.toLabel(whiteWineType)} />
          <Tooltip.Gap space=#xxsmall />
        </>)}
        {information.region->Option.mapWithDefault(React.null, region => {
          let formattedRegion = region ++ ", " ++ information.country
          <>
            <Tooltip.Span text={t("Region") ++ t(":") ++ " "} tone=#faded />
            <Tooltip.Span text=formattedRegion />
            <Tooltip.Gap space=#xxsmall />
          </>
        })}
        <Tooltip.Span text={t("Category") ++ t(":") ++ " "} tone=#faded />
        <Tooltip.Span text=information.categoryName />
        <Tooltip.Gap space=#xxsmall />
        {information.alcoholVolume->Option.mapWithDefault(React.null, alcoholVolume => <>
          <Tooltip.Span text={t("Alcohol volume") ++ t(":") ++ " "} tone=#faded />
          <Tooltip.Span text=alcoholVolume />
          <Tooltip.Gap space=#xxsmall />
        </>)}
      </>

    let {?style, ?className} = styleProps()

    <div ?style ?className>
      <Tooltip
        content
        arrowed=false
        placement=#"bottom start"
        offset=0.
        crossOffset=2.
        delay=125
        closeDelay=50
        onOpenChange={opened => setTooltipOpened(_ => opened)}>
        <Svg
          width="20" height="14" viewBox="0 0 14 14" style={ReactDOMStyle.make(~height="100%", ())}>
          <Svg.Path
            d="M6 11.2A5.2 5.2 0 1 0 .8 6v4.2a1 1 0 0 0 1 1H6z"
            fill={tooltipOpened ? "#25243A" : "transparent"}
            transform="translate(0, 1)"
          />
          <Svg.Path
            fill={tooltipOpened ? "#FFF" : "#A2A1B0"}
            d={tooltipOpened
              ? "M6.99997 10C7.27611 10 7.49997 9.77614 7.49997 9.5L7.49998 7.49998L9.5 7.5C9.77614 7.5 10 7.27614 10 7C10 6.72386 9.77614 6.5 9.5 6.5L7.49999 6.5L7.50001 4.5C7.50001 4.22386 7.27615 4 7.00001 4C6.72387 4 6.50001 4.22386 6.50001 4.5L6.50001 6.5L4.5 6.5C4.22386 6.5 4 6.72382 4 6.99996C4 7.2761 4.22386 7.49996 4.5 7.49996L6.50001 7.49998L6.50001 9.5C6.50001 9.77614 6.72383 10 6.99997 10Z"
              : "M6.99997 11C7.27611 11 7.49997 10.7761 7.49997 10.5L7.49999 7.49998L10.5 7.5C10.7761 7.5 11 7.27614 11 7C11 6.72386 10.7761 6.5 10.5 6.5L7.49999 6.5L7.50001 3.5C7.50001 3.22386 7.27615 3 7.00001 3C6.72387 3 6.50001 3.22386 6.50001 3.5L6.50001 6.5L3.5 6.5C3.22386 6.5 3 6.72382 3 6.99996C3 7.2761 3.22386 7.49996 3.5 7.49996L6.50001 7.49998L6.50001 10.5C6.50001 10.7761 6.72383 11 6.99997 11Z"}
            transform="translate(-1, 0)"
          />
        </Svg>
      </Tooltip>
    </div>
  }
}

module PressableIconIndicator = {
  @inline let wrapperFont = `normal 400 12px "Libre Franklin"`

  let styles = StyleX.create({
    "root": style(
      ~width="21px",
      ~height="24px",
      ~position=#absolute,
      ~right="-30px",
      ~margin="2px 0",
      ~display=#flex,
      ~alignItems=#center,
      ~justifyContent=#center,
      (),
    ),
  })

  let styleProps = (~hovered) =>
    StyleX.props([
      styles["root"],
      style(
        ~width=hovered ? "21px" : "0px",
        ~opacity=hovered ? 1. : 0.,
        ~backgroundColor=hovered ? Colors.neutralColor10 : Colors.transparent,
        ~boxShadow=`${hovered ? Colors.neutralColor10 : Colors.transparent} -8px 0 13px 10px`,
        ~transition="width .15s ease, opacity .15s ease, backgroundColor .05s ease, boxShadow .05s ease",
        (),
      ),
    ])

  @react.component
  let make = (~hovered) => {
    let {?style, ?className} = styleProps(~hovered)

    <div ?style ?className>
      <Svg width="14" height="16" viewBox="0 0 20 20">
        <Svg.Path
          d="M 11.236 4.018 L 16.683 9.464 L 1.97 9.464 C 1.559 9.464 1.301 9.91 1.508 10.267 C 1.603 10.433 1.779 10.535 1.97 10.535 L 16.799 10.535 L 11.352 15.981 C 11.06 16.272 11.194 16.768 11.592 16.876 C 11.775 16.926 11.973 16.873 12.108 16.738 L 18.408 10.436 C 18.618 10.227 18.618 9.887 18.408 9.679 L 11.991 3.263 C 11.784 3.053 11.444 3.053 11.236 3.263 C 11.027 3.47 11.027 3.81 11.236 4.018 Z"
          fill="#25243A"
        />
      </Svg>
    </div>
  }
}

@inline let primaryFont = `normal 600 13px "Libre Franklin"`
@inline let secondaryFont = `normal 400 11px "Libre Franklin"`

let styles = StyleX.create({
  "root": style(
    ~position=#relative,
    ~display=#flex,
    ~flexDirection=#row,
    ~gap=Spaces.xsmallPx,
    ~width="100%",
    ~height="44px",
    ~marginTop="1px",
    ~cursor=#pointer,
    (),
  ),
  "pastille": style(~minWidth="8px", ~paddingTop="4.5px", ()),
  "separator": style(
    ~minWidth=Spaces.xxsmallPx,
    ~height="15px",
    ~backgroundColor=Colors.neutralColor30,
    ~margin=`0 ${Spaces.smallPx}`,
    (),
  ),
  "primaryTextProductName": style(
    ~flex="1",
    ~lineHeight=Spaces.mediumPx,
    ~minWidth=Spaces.largePx,
    ~maxWidth="fit-content",
    ~font=primaryFont,
    ~color=Colors.neutralColor90,
    ~textOverflow=#ellipsis,
    ~overflow=#hidden,
    ~whiteSpace=#nowrap,
    (),
  ),
  "primaryTextVariantName": style(
    ~lineHeight=Spaces.mediumPx,
    ~font=primaryFont,
    ~color=Colors.neutralColor90,
    ~textOverflow=#ellipsis,
    ~overflow=#hidden,
    ~whiteSpace=#nowrap,
    (),
  ),
  "primaryTextDisabled": style(
    ~color=Colors.neutralColor50,
    ~transform="skewX(-12deg)", // NOTE - fontStyle italic is clipped
    ~fontWeight=#400,
    (),
  ),
  "secondaryTextDescription": style(
    ~lineHeight=Spaces.mediumPx,
    ~font=secondaryFont,
    ~color=Colors.neutralColor50,
    ~textOverflow=#ellipsis,
    ~overflow=#hidden,
    ~whiteSpace=#nowrap,
    (),
  ),
})
let styleProps = (~disabled) =>
  StyleX.props([styles["root"], disabled ? style(~cursor=#auto, ()) : style()])
let pastilleStyleProps = () => StyleX.props([styles["pastille"]])
let productNameStyleProps = (~disabled) =>
  StyleX.props([
    styles["primaryTextProductName"],
    disabled ? styles["primaryTextDisabled"] : style(),
  ])
let variantNameStyleProps = (~disabled) =>
  StyleX.props([
    styles["primaryTextVariantName"],
    disabled ? styles["primaryTextDisabled"] : style(),
  ])
let descriptionNameStyleProps = () => StyleX.props([styles["secondaryTextDescription"]])
let separatorStyleProps = (~disabled) =>
  StyleX.props([
    styles["separator"],
    disabled ? style(~transform="skewX(-12deg)", ~opacity=0.7, ~marginRight="6px", ()) : style(),
  ])

@react.component
let make = (
  ~redirectRoute=?,
  ~badge=?,
  ~disabled=false,
  ~errorMessage=?,
  ~productKind,
  ~information,
  ~hideProducerAndSupplier=false,
) => {
  let (ref, hovered) = Hover.use()

  let {?className} = styleProps(~disabled)
  let {style: ?pastilleStyle, className: ?pastilleClassName} = pastilleStyleProps()
  let {style: ?separatorStyle, className: ?separatorClassName} = separatorStyleProps(~disabled)
  let {style: ?productNameStyle, className: ?productNameClassName} = {
    productNameStyleProps(~disabled)
  }
  let {style: ?variantNameStyle, className: ?variantNameClassName} = {
    variantNameStyleProps(~disabled)
  }
  let {
    style: ?descriptionNameStyle,
    className: ?descriptionNameClassName,
  } = descriptionNameStyleProps()

  let to = redirectRoute->Option.mapWithDefault(Navigation.Route(""), route => Route(route))
  let formattedDescription = CatalogProduct.Information.formatDescription(
    ~productKind,
    ~information,
    ~hideColor=true,
    ~hideProducer=hideProducerAndSupplier,
    ~hideSupplier=hideProducerAndSupplier,
    (),
  )
  let pastilleColor =
    information.color->Option.map(color =>
      (color->CatalogProduct.Color.toColorSet(~variation=#pastille)).foregroundColor
    )

  <View ref style={StyleX.props([style(~display=#contents, ~width="100%", ())])}>
    <Navigation.Link to disabled={redirectRoute->Option.isNone || disabled} ?className>
      <div style=?pastilleStyle className=?pastilleClassName>
        <Pastille color=?pastilleColor />
      </div>
      <Stack space=#none>
        <View style={StyleX.props([style(~display=#flex, ~alignItems=#center, ())])}>
          <span
            title=information.productName style=?productNameStyle className=?productNameClassName>
            {information.productName->React.string}
          </span>
          <div style=?separatorStyle className=?separatorClassName />
          <span style=?variantNameStyle className=?variantNameClassName>
            {information.variantName->React.string}
          </span>
          {switch badge {
          | Some({variation, text}) if !disabled =>
            <Box spaceLeft=#small>
              <Badge variation size=#small> {text->React.string} </Badge>
            </Box>
          | _ => React.null
          }}
        </View>
        <View style={StyleX.props([style(~display=#flex, ~alignItems=#center, ())])}>
          {switch errorMessage {
          | Some(message) =>
            <TextStyle size=#tiny variation=#negative> {message->React.string} </TextStyle>
          | None =>
            <>
              <span
                title=formattedDescription
                style=?descriptionNameStyle
                className=?descriptionNameClassName>
                {formattedDescription->React.string}
              </span>
              <TooltipIconTrigger information />
            </>
          }}
        </View>
      </Stack>
      {if redirectRoute->Option.isSome {
        <PressableIconIndicator hovered />
      } else {
        React.null
      }}
    </Navigation.Link>
  </View>
}
